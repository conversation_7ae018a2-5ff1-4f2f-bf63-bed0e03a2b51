version: '3.3'

services:
  zrdds-ui:
    platform: linux/s390x
    build:
        context: .
        dockerfile: docker/Dockerfile
    image: icr.io/zoa-oci/zaa-topology-ui:dev-s390x
    ports:
    - 4000:4000
    networks:
        - ibmzaiops_zaiops
    hostname: zrddsui
    container_name: zrdds-ui
    volumes:
      - output:/usr/share/nginx/html/output

volumes:
  output:

networks:
    ibmzaiops_zaiops:
        external: true
