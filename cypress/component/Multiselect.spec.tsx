import {
    resetData,
    assertDecoratorWithText,
    mountApp,
    applyAllNodesToolbarSettings,
    toggleNodeCheckbox,
    nodeShouldBeSelected,
    nodeShouldNotBeSelected,
    collapseDSG,
    expandDSG,
    nodeShouldBePartiallySelected,
    nodeShouldExist,
    nodeShouldNotExist,
    interceptResourceGroupSummaryApiCall
} from "./Utils/testUtils";
import {
    CICSAB51,
    CICSCWA1,
    CICSCWA3,
    DB1GRP,
    DB2D,
    DB4D,
    DB5D,
    DB6D,
    DB7D,
    DB8D,
    Q9G1,
    SYSG
} from "./Utils/FrequentTestNodes";

describe("Multiselect tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it("Single node can be selected and deselected", () => {
        mountApp();
        nodeShouldNotBeSelected(CICSCWA1);
        toggleNodeCheckbox(CICSCWA1);
        nodeShouldBeSelected(CICSCWA1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "1 resource selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        toggleNodeCheckbox(CICSCWA1);
        nodeShouldNotBeSelected(CICSCWA1);
        cy.get("[data-cy='resources-bar-selected-count']").should("not.exist");
    });

    it("group node can be all selected, partially selected and deselected", () => {
        mountApp();

        // Select on group node which selects all
        nodeShouldNotBeSelected(DB1GRP);
        toggleNodeCheckbox(DB1GRP);
        nodeShouldBeSelected(DB1GRP);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "6 resources selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        // Check that nodes inside are all selected
        expandDSG(DB1GRP);
        nodeShouldBeSelected(DB2D);
        nodeShouldBeSelected(DB4D);
        nodeShouldBeSelected(DB5D);
        nodeShouldBeSelected(DB6D);
        nodeShouldBeSelected(DB7D);
        nodeShouldBeSelected(DB8D);
        collapseDSG(DB1GRP);

        // Deselect on group node which should deselect all
        toggleNodeCheckbox("DB1GRP-LPAR400J-DB2DataSharingGroup");
        nodeShouldNotBeSelected("DB1GRP-LPAR400J-DB2DataSharingGroup");
        cy.get("[data-cy='resources-bar-selected-count']").should("not.exist");

        // Check that nodes inside are all deselected
        expandDSG(DB1GRP);
        nodeShouldNotBeSelected(DB2D);
        nodeShouldNotBeSelected(DB4D);
        nodeShouldNotBeSelected(DB5D);
        nodeShouldNotBeSelected(DB6D);
        nodeShouldNotBeSelected(DB7D);
        nodeShouldNotBeSelected(DB8D);

        // Select single and group node should be partially selected
        toggleNodeCheckbox(DB2D);
        nodeShouldBeSelected(DB2D);
        collapseDSG(DB1GRP);
        nodeShouldBePartiallySelected("DB1GRP-LPAR400J-DB2DataSharingGroup");

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "1 resource selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        // Clicking on partially selected will select all
        toggleNodeCheckbox("DB1GRP-LPAR400J-DB2DataSharingGroup");
        nodeShouldBeSelected("DB1GRP-LPAR400J-DB2DataSharingGroup");

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "6 resources selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        // Check that nodes inside are all selected
        expandDSG("DB1GRP-LPAR400J-DB2DataSharingGroup");
        nodeShouldBeSelected(DB2D);
        nodeShouldBeSelected(DB4D);
        nodeShouldBeSelected(DB5D);
        nodeShouldBeSelected(DB6D);
        nodeShouldBeSelected(DB7D);
        nodeShouldBeSelected(DB8D);

        // Deselect one so group node becomes partially selected
        toggleNodeCheckbox(DB2D);
        nodeShouldNotBeSelected(DB2D);
        collapseDSG(DB1GRP);
        nodeShouldBePartiallySelected(DB1GRP);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "5 resources selected");
    });

    it(`ITOA-17493 unmonitored nodes should not disappear after deselecting all resources one by one`, () => {
        mountApp();

        assertDecoratorWithText("SYSG-ZOS", "Critical");
        // Unmonitored nodes should not exist
        cy.get("[data-testid='CICSAB51-SYS-CICSRegion']").should("not.exist");

        applyAllNodesToolbarSettings();
        // Unmonitored nodes should exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(CICSCWA1);
        nodeShouldBeSelected(CICSCWA1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "1 resource selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(Q9G1);
        nodeShouldBeSelected(Q9G1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "2 resources selected");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(Q9G1);
        nodeShouldNotBeSelected(Q9G1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "1 resource selected");

        toggleNodeCheckbox(CICSCWA1);
        nodeShouldNotBeSelected(CICSCWA1);

        cy.get("[data-cy='resources-bar-selected-count']").should("not.exist");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);
    });

    it(`ITOA-17493 unmonitored nodes should not disappear after deselecting all resources using cancel`, () => {
        mountApp();

        assertDecoratorWithText(SYSG, "Critical");
        // Unmonitored nodes should not exist
        nodeShouldNotExist(CICSAB51);

        applyAllNodesToolbarSettings();
        // Unmonitored nodes should exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(CICSCWA1);
        nodeShouldBeSelected(CICSCWA1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "1 resource selected");
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("not.be.disabled");
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(Q9G1);
        nodeShouldBeSelected(Q9G1);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "2 resources selected");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);

        toggleNodeCheckbox(DB1GRP);
        nodeShouldBeSelected(DB1GRP);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "8 resources selected");

        toggleNodeCheckbox(CICSCWA3);
        nodeShouldBeSelected(CICSCWA3);

        cy.get("[data-cy='resources-bar-selected-count']")
            .should("be.visible")
            .should("contain", "9 resources selected");

        // View resource details button should be disabled because we selected more than 8 resources
        cy.get("[data-cy='resources-bar-view-resource']")
            .should("be.visible")
            .should("be.disabled");

        // Click cancel and none should be selected
        cy.get("[data-cy='resources-bar-cancel']").should("be.visible").click();

        nodeShouldNotBeSelected(CICSCWA1);
        nodeShouldNotBeSelected(Q9G1);
        nodeShouldNotBeSelected(DB1GRP);
        nodeShouldNotBeSelected(CICSCWA3);

        cy.get("[data-cy='resources-bar-selected-count']").should("not.exist");

        // Unmonitored nodes should still exist
        nodeShouldExist(CICSAB51);
    });
});
