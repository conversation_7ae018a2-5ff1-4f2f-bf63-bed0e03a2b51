export const GraphDataMegaNodeFixture = {
    label: "",
    nodes: {
        "CICSCB08-SYS-CICSRegion": {
            id: "CICSCB08-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCB08-SYS",
                name: "CICSCB08",
                applID: "CICSCB08",
                netID: "USCACO01",
                jobName: "CICSCB08",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2023-06-07T02:23:58Z"
            }
        },
        "DA1D-SYS-DB2Subsystem": {
            id: "DA1D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DA1D",
                label: "DA1D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DA1D",
                versionString: "10.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: "",
                "Data sharing group": "DA1GRP"
            }
        },
        "CICSYX01-SYS-CICSRegion": {
            id: "CICSYX01-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSYX01-SYS",
                name: "CICSYX01",
                applID: "CICSYX01",
                netID: "USCACO01",
                jobName: "CICSYX01",
                version: "5.5.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "IC1C-SYS-IMSSubsystem": {
            id: "IC1C-SYS-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2022-01-04T03:08:50Z",
                label: "IC1C-SYS",
                name: "IC1C",
                keyName: "AppServer",
                versionString: "12.1",
                commandPrefixName: "IC1C",
                controllingAddressSpace: "IMSC10CC",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "840597649",
                programsChecksum: "436537747",
                transactionsChecksum: "272056237"
            }
        },
        "CICSYXG6-SYS-CICSRegion": {
            id: "CICSYXG6-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSYXG6-SYS",
                name: "CICSYXG6",
                applID: "CICSYXG6",
                netID: "USCACO01",
                jobName: "CICSYXG6",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "Q9G1-SYS-MQSubsystem": {
            id: "Q9G1-SYS-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2022-01-04T03:08:50Z",
                label: "Q9G1-SYS",
                name: "Q9G1",
                commandPrefixName: ">Q9G1",
                controllingAddressSpace: "Q9G1MSTR",
                versionString: "9.0.5"
            }
        },
        "CICSCWA2-SYS-CICSRegion": {
            id: "CICSCWA2-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA2-SYS",
                name: "CICSCWA2",
                applID: "CICSCWA2",
                netID: "USCACO01",
                jobName: "CICSCWA2",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DB1GRP-LPAR400J",
                name: "DB1GRP",
                groupAttachName: "DB1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA1-SYS-CICSRegion": {
            id: "CICSCWA1-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA1-SYS",
                name: "CICSCWA1",
                applID: "CICSCWA1",
                netID: "USCACO01",
                jobName: "CICSCWA1",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CSLIC1C-IMSSysplexGroup": {
            id: "CSLIC1C-IMSSysplexGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSysplexGroup",
            metadata: {
                source: "DLA",
                scanDate: "2022-01-04T03:08:50Z",
                label: "CSLIC1C-LPAR400J",
                name: "CSLIC1C",
                groupFunction: "IMSPlex"
            }
        },
        "CICSAB51-SYS-CICSRegion": {
            id: "CICSAB51-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSAB51-SYS",
                name: "CICSAB51",
                applID: "CICSAB51",
                netID: "USCACO01",
                jobName: "CICSAB51",
                version: "5.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DC1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DC1GRP-LPAR400J",
                name: "DC1GRP",
                groupAttachName: "DC1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1B-SYS-DB2Subsystem": {
            id: "DB1B-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1B",
                label: "DB1B-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1B",
                versionString: "11.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DC1V-SYS-DB2Subsystem": {
            id: "DC1V-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1V",
                label: "DC1V-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1V",
                versionString: "12.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DA1X-SYS-DB2Subsystem": {
            id: "DA1X-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DA1X",
                label: "DA1X-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DA1X",
                versionString: "10.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DC1W-SYS-DB2Subsystem": {
            id: "DC1W-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1W",
                label: "DC1W-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1W",
                versionString: "12.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DB1D-SYS-DB2Subsystem": {
            id: "DB1D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1D",
                label: "DB1D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1D",
                versionString: "11.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: "",
                "Data sharing group": "DB1GRP"
            }
        },
        "DA1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DA1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DA1GRP-LPAR400J",
                name: "DA1GRP",
                groupAttachName: "DA1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCB05-SYS-CICSRegion": {
            id: "CICSCB05-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCB05-SYS",
                name: "CICSCB05",
                applID: "CICSCB05",
                netID: "USCACO01",
                jobName: "CICSCB05",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2023-06-07T02:23:58Z"
            }
        },
        "CICSCWB2-SYS-CICSRegion": {
            id: "CICSCWB2-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWB2-SYS",
                name: "CICSCWB2",
                applID: "CICSCWB2",
                netID: "USCACO01",
                jobName: "CICSCWB2",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "SYSG-ZOS": {
            id: "SYSG-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "SYS-LPAR400J",
                name: "SYSG",
                smfId: "SYS",
                netId: "USCACO01",
                sscp: "CCCDRM25",
                fqdn: "wlag2.svl.ibm.com",
                osName: "zOS",
                version: "02.04.00",
                sysResVolume: "G2401C",
                iplParmDataset: "SYS2.IPLPARM",
                iplParmMember: "LOADGG",
                iplParmDevice: "8358",
                iplParmVolume: "G2401C",
                osFriendlyName: "LNKLST00",
                osRsuLevel: "0",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1Y-SYS-DB2Subsystem": {
            id: "DC1Y-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1Y",
                label: "DC1Y-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1Y",
                versionString: "12.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DB1A-SYS-DB2Subsystem": {
            id: "DB1A-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1A",
                label: "DB1A-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1A",
                versionString: "11.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DC1K-SYS-DB2Subsystem": {
            id: "DC1K-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1K",
                label: "DC1K-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1K",
                versionString: "",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "CICSCB06-SYS-CICSRegion": {
            id: "CICSCB06-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCB06-SYS",
                name: "CICSCB06",
                applID: "CICSCB06",
                netID: "USCACO01",
                jobName: "CICSCB06",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2023-06-07T02:23:58Z"
            }
        },
        "LPAR400J-Sysplex": {
            id: "LPAR400J-Sysplex",
            dataPlatform: "ZDiscovery",
            nodeType: "Sysplex",
            metadata: {
                source: "DLA",
                name: "LPAR400J",
                label: "LPAR400J",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWB1-SYS-CICSRegion": {
            id: "CICSCWB1-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWB1-SYS",
                name: "CICSCWB1",
                applID: "CICSCWB1",
                netID: "USCACO01",
                jobName: "CICSCWB1",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCB07-SYS-CICSRegion": {
            id: "CICSCB07-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCB07-SYS",
                name: "CICSCB07",
                applID: "CICSCB07",
                netID: "USCACO01",
                jobName: "CICSCB07",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2023-06-07T02:23:58Z"
            }
        },
        "DC1E-SYS-DB2Subsystem": {
            id: "DC1E-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1E",
                label: "DC1E-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1E",
                versionString: "12.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: "",
                "Data sharing group": "DC1GRP"
            }
        },
        "DC1X-SYS-DB2Subsystem": {
            id: "DC1X-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1X",
                label: "DC1X-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1X",
                versionString: "12.1.0",
                scanDate: "2022-01-04T03:08:50Z",
                DDFLocation: ""
            }
        },
        "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup": {
            id: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DSNDBWG-UTCPLXJ8",
                name: "DSNDBWG",
                groupAttachName: "DBWG",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5T9A-J90-CICSRegion": {
            id: "CICS5T9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5T9A-J90",
                name: "CICS5T9A",
                applID: "CICS5T9A",
                netID: "USIBMT6",
                jobName: "CICS5T9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS3A8B-J80-CICSRegion": {
            id: "CICS3A8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A8B-J80",
                name: "CICS3A8B",
                applID: "CICS3A8B",
                netID: "USIBMT6",
                jobName: "CICS3A8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS1A8B-J80-CICSRegion": {
            id: "CICS1A8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A8B-J80",
                name: "CICS1A8B",
                applID: "CICS1A8B",
                netID: "USIBMT6",
                jobName: "CICS1A8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "IMS8-J80-IMSSubsystem": {
            id: "IMS8-J80-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T14:28:17Z",
                label: "IMS8-J80",
                name: "IMS8",
                keyName: "AppServer",
                versionString: "15.1",
                commandPrefixName: "IMS8",
                controllingAddressSpace: "IMS8",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "455324549",
                programsChecksum: "797718565",
                transactionsChecksum: "478786710"
            }
        },
        "CICS2T9C-J90-CICSRegion": {
            id: "CICS2T9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T9C-J90",
                name: "CICS2T9C",
                applID: "CICS2T9C",
                netID: "USIBMT6",
                jobName: "CICS2T9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5A9A-J90-CICSRegion": {
            id: "CICS5A9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A9A-J90",
                name: "CICS5A9A",
                applID: "CICS5A9A",
                netID: "USIBMT6",
                jobName: "CICS5A9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICSCA8C-J80-CICSRegion": {
            id: "CICSCA8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCA8C-J80",
                name: "CICSCA8C",
                applID: "CICSCA8C",
                netID: "USIBMT6",
                jobName: "CICSCA8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS6A9B-J90-CICSRegion": {
            id: "CICS6A9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A9B-J90",
                name: "CICS6A9B",
                applID: "CICS6A9B",
                netID: "USIBMT6",
                jobName: "CICS6A9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS1A9B-J90-CICSRegion": {
            id: "CICS1A9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A9B-J90",
                name: "CICS1A9B",
                applID: "CICS1A9B",
                netID: "USIBMT6",
                jobName: "CICS1A9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2T8A-J80-CICSRegion": {
            id: "CICS2T8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T8A-J80",
                name: "CICS2T8A",
                applID: "CICS2T8A",
                netID: "USIBMT6",
                jobName: "CICS2T8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2T8C-J80-CICSRegion": {
            id: "CICS2T8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T8C-J80",
                name: "CICS2T8C",
                applID: "CICS2T8C",
                netID: "USIBMT6",
                jobName: "CICS2T8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "DBW1-J80-DB2Subsystem": {
            id: "DBW1-J80-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBW1",
                label: "DBW1-J80",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBW1",
                versionString: "13.1.0",
                scanDate: "2024-05-09T14:28:17Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBWG"
            }
        },
        "CICSCT8A-J80-CICSRegion": {
            id: "CICSCT8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCT8A-J80",
                name: "CICSCT8A",
                applID: "CICSCT8A",
                netID: "USIBMT6",
                jobName: "CICSCT8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS3W8A-J80-CICSRegion": {
            id: "CICS3W8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3W8A-J80",
                name: "CICS3W8A",
                applID: "CICS3W8A",
                netID: "USIBMT6",
                jobName: "CICS3W8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A8E-J80-CICSRegion": {
            id: "CICS2A8E-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8E-J80",
                name: "CICS2A8E",
                applID: "CICS2A8E",
                netID: "USIBMT6",
                jobName: "CICS2A8E",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS6T9B-J90-CICSRegion": {
            id: "CICS6T9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6T9B-J90",
                name: "CICS6T9B",
                applID: "CICS6T9B",
                netID: "USIBMT6",
                jobName: "CICS6T9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2A8C-J80-CICSRegion": {
            id: "CICS2A8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8C-J80",
                name: "CICS2A8C",
                applID: "CICS2A8C",
                netID: "USIBMT6",
                jobName: "CICS2A8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A8A-J80-CICSRegion": {
            id: "CICS2A8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8A-J80",
                name: "CICS2A8A",
                applID: "CICS2A8A",
                netID: "USIBMT6",
                jobName: "CICS2A8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CSQ9-J90-MQSubsystem": {
            id: "CSQ9-J90-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T12:26:38Z",
                label: "CSQ9-J90",
                name: "CSQ9",
                commandPrefixName: "!MQJ90",
                controllingAddressSpace: "CSQ9MSTR",
                versionString: ""
            }
        },
        "DBT1-J90-DB2Subsystem": {
            id: "DBT1-J90-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBT1",
                label: "DBT1-J90",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBT1",
                versionString: "13.1.0",
                scanDate: "2024-05-09T12:26:38Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBTG"
            }
        },
        "CICS5A9C-J90-CICSRegion": {
            id: "CICS5A9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A9C-J90",
                name: "CICS5A9C",
                applID: "CICS5A9C",
                netID: "USIBMT6",
                jobName: "CICS5A9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "QMQ7-J90-MQSubsystem": {
            id: "QMQ7-J90-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T12:26:38Z",
                label: "QMQ7-J90",
                name: "QMQ7",
                commandPrefixName: "!QMQ7",
                controllingAddressSpace: "QMQ7MSTR",
                versionString: ""
            }
        },
        "CICS5AIB-J90-CICSRegion": {
            id: "CICS5AIB-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5AIB-J90",
                name: "CICS5AIB",
                applID: "CICS5AIB",
                netID: "USIBMT6",
                jobName: "CICS5AIB",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS3T8B-J80-CICSRegion": {
            id: "CICS3T8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3T8B-J80",
                name: "CICS3T8B",
                applID: "CICS3T8B",
                netID: "USIBMT6",
                jobName: "CICS3T8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS1T8A-J80-CICSRegion": {
            id: "CICS1T8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1T8A-J80",
                name: "CICS1T8A",
                applID: "CICS1T8A",
                netID: "USIBMT6",
                jobName: "CICS1T8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A9A-J90-CICSRegion": {
            id: "CICS2A9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A9A-J90",
                name: "CICS2A9A",
                applID: "CICS2A9A",
                netID: "USIBMT6",
                jobName: "CICS2A9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2A9C-J90-CICSRegion": {
            id: "CICS2A9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A9C-J90",
                name: "CICS2A9C",
                applID: "CICS2A9C",
                netID: "USIBMT6",
                jobName: "CICS2A9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS6T8A-J80-CICSRegion": {
            id: "CICS6T8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6T8A-J80",
                name: "CICS6T8A",
                applID: "CICS6T8A",
                netID: "USIBMT6",
                jobName: "CICS6T8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "QMQ1-J90-MQSubsystem": {
            id: "QMQ1-J90-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T12:26:38Z",
                label: "QMQ1-J90",
                name: "QMQ1",
                commandPrefixName: "!QMQ1",
                controllingAddressSpace: "QMQ1MSTR",
                versionString: ""
            }
        },
        "CICS3T9B-J90-CICSRegion": {
            id: "CICS3T9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3T9B-J90",
                name: "CICS3T9B",
                applID: "CICS3T9B",
                netID: "USIBMT6",
                jobName: "CICS3T9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2T9A-J90-CICSRegion": {
            id: "CICS2T9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T9A-J90",
                name: "CICS2T9A",
                applID: "CICS2T9A",
                netID: "USIBMT6",
                jobName: "CICS2T9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DSNDBDG-UTCPLXJ8-DB2DataSharingGroup": {
            id: "DSNDBDG-UTCPLXJ8-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DSNDBDG-UTCPLXJ8",
                name: "DSNDBDG",
                groupAttachName: "DBDG",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DSNDBSG-UTCPLXJ8-DB2DataSharingGroup": {
            id: "DSNDBSG-UTCPLXJ8-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DSNDBSG-UTCPLXJ8",
                name: "DSNDBSG",
                groupAttachName: "DBSG",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A8F-J80-CICSRegion": {
            id: "CICS2A8F-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8F-J80",
                name: "CICS2A8F",
                applID: "CICS2A8F",
                netID: "USIBMT6",
                jobName: "CICS2A8F",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS3A9B-J90-CICSRegion": {
            id: "CICS3A9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A9B-J90",
                name: "CICS3A9B",
                applID: "CICS3A9B",
                netID: "USIBMT6",
                jobName: "CICS3A9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "IMS9-J90-IMSSubsystem": {
            id: "IMS9-J90-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T12:26:38Z",
                label: "IMS9-J90",
                name: "IMS9",
                keyName: "AppServer",
                versionString: "15.1",
                commandPrefixName: "IMS9",
                controllingAddressSpace: "IMS9",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "455324549",
                programsChecksum: "797718565",
                transactionsChecksum: "478786710"
            }
        },
        "DBD3-J90-DB2Subsystem": {
            id: "DBD3-J90-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBD3",
                label: "DBD3-J90",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBD3",
                versionString: "13.1.0",
                scanDate: "2024-05-09T12:26:38Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBDG"
            }
        },
        "CICSCA8A-J80-CICSRegion": {
            id: "CICSCA8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCA8A-J80",
                name: "CICSCA8A",
                applID: "CICSCA8A",
                netID: "USIBMT6",
                jobName: "CICSCA8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS1A8C-J80-CICSRegion": {
            id: "CICS1A8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A8C-J80",
                name: "CICS1A8C",
                applID: "CICS1A8C",
                netID: "USIBMT6",
                jobName: "CICS1A8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CSQ8-J80-MQSubsystem": {
            id: "CSQ8-J80-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T14:28:17Z",
                label: "CSQ8-J80",
                name: "CSQ8",
                commandPrefixName: "!MQJ80",
                controllingAddressSpace: "CSQ8MSTR",
                versionString: ""
            }
        },
        "CICS7AGB-J90-CICSRegion": {
            id: "CICS7AGB-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS7AGB-J90",
                name: "CICS7AGB",
                applID: "CICS7AGB",
                netID: "USIBMT6",
                jobName: "CICS7AGB",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5A8B-J80-CICSRegion": {
            id: "CICS5A8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A8B-J80",
                name: "CICS5A8B",
                applID: "CICS5A8B",
                netID: "USIBMT6",
                jobName: "CICS5A8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "DBWG-J80-DB2Subsystem": {
            id: "DBWG-J80-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBWG",
                label: "DBWG-J80",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBWG",
                versionString: "13.1.0",
                scanDate: "2024-05-09T14:28:17Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBWG"
            }
        },
        "CICS6A8C-J80-CICSRegion": {
            id: "CICS6A8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A8C-J80",
                name: "CICS6A8C",
                applID: "CICS6A8C",
                netID: "USIBMT6",
                jobName: "CICS6A8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICSWUI8-J80-CICSRegion": {
            id: "CICSWUI8-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSWUI8-J80",
                name: "CICSWUI8",
                applID: "CICSWUI8",
                netID: "USIBMT6",
                jobName: "CICSWUI8",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS3A8C-J80-CICSRegion": {
            id: "CICS3A8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A8C-J80",
                name: "CICS3A8C",
                applID: "CICS3A8C",
                netID: "USIBMT6",
                jobName: "CICS3A8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS3A8A-J80-CICSRegion": {
            id: "CICS3A8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A8A-J80",
                name: "CICS3A8A",
                applID: "CICS3A8A",
                netID: "USIBMT6",
                jobName: "CICS3A8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "J80-ZOS": {
            id: "J80-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "J80-UTCPLXJ8",
                name: "J80",
                smfId: "J80",
                netId: "USIBMT6",
                sscp: "PETJ80CP",
                fqdn: "j80.pok.stglabs.ibm.com",
                osName: "zOS",
                version: "03.01.00",
                sysResVolume: "PETPF0",
                iplParmDataset: "SYS0.IPLPARM",
                iplParmMember: "LOADPT",
                iplParmDevice: "5D01",
                iplParmVolume: "PETPF0",
                osFriendlyName: "LNKLSTJ2",
                osRsuLevel: "0",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS3T8A-J80-CICSRegion": {
            id: "CICS3T8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3T8A-J80",
                name: "CICS3T8A",
                applID: "CICS3T8A",
                netID: "USIBMT6",
                jobName: "CICS3T8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICSCA8B-J80-CICSRegion": {
            id: "CICSCA8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCA8B-J80",
                name: "CICSCA8B",
                applID: "CICSCA8B",
                netID: "USIBMT6",
                jobName: "CICSCA8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CSLPROD-IMSSysplexGroup": {
            id: "CSLPROD-IMSSysplexGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSysplexGroup",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T12:26:38Z",
                label: "CSLPROD-UTCPLXJ8",
                name: "CSLPROD",
                groupFunction: "IMSPlex"
            }
        },
        "MQ81-J80-MQSubsystem": {
            id: "MQ81-J80-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T14:28:17Z",
                label: "MQ81-J80",
                name: "MQ81",
                commandPrefixName: "!MQJ81",
                controllingAddressSpace: "MQ81MSTR",
                versionString: ""
            }
        },
        "DSNDBTG-UTCPLXJ8-DB2DataSharingGroup": {
            id: "DSNDBTG-UTCPLXJ8-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DSNDBTG-UTCPLXJ8",
                name: "DSNDBTG",
                groupAttachName: "DBTG",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS1A8A-J80-CICSRegion": {
            id: "CICS1A8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A8A-J80",
                name: "CICS1A8A",
                applID: "CICS1A8A",
                netID: "USIBMT6",
                jobName: "CICS1A8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS6A8A-J80-CICSRegion": {
            id: "CICS6A8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A8A-J80",
                name: "CICS6A8A",
                applID: "CICS6A8A",
                netID: "USIBMT6",
                jobName: "CICS6A8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A8B-J80-CICSRegion": {
            id: "CICS2A8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8B-J80",
                name: "CICS2A8B",
                applID: "CICS2A8B",
                netID: "USIBMT6",
                jobName: "CICS2A8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS1A9C-J90-CICSRegion": {
            id: "CICS1A9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A9C-J90",
                name: "CICS1A9C",
                applID: "CICS1A9C",
                netID: "USIBMT6",
                jobName: "CICS1A9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2T8B-J80-CICSRegion": {
            id: "CICS2T8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T8B-J80",
                name: "CICS2T8B",
                applID: "CICS2T8B",
                netID: "USIBMT6",
                jobName: "CICS2T8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS5AIC-J90-CICSRegion": {
            id: "CICS5AIC-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5AIC-J90",
                name: "CICS5AIC",
                applID: "CICS5AIC",
                netID: "USIBMT6",
                jobName: "CICS5AIC",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5AIA-J90-CICSRegion": {
            id: "CICS5AIA-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5AIA-J90",
                name: "CICS5AIA",
                applID: "CICS5AIA",
                netID: "USIBMT6",
                jobName: "CICS5AIA",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2F9A-J90-CICSRegion": {
            id: "CICS2F9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2F9A-J90",
                name: "CICS2F9A",
                applID: "CICS2F9A",
                netID: "USIBMT6",
                jobName: "CICS2F9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "UTCPLXJ8-Sysplex": {
            id: "UTCPLXJ8-Sysplex",
            dataPlatform: "ZDiscovery",
            nodeType: "Sysplex",
            metadata: {
                source: "DLA",
                name: "UTCPLXJ8",
                label: "UTCPLXJ8",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5A9B-J90-CICSRegion": {
            id: "CICS5A9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A9B-J90",
                name: "CICS5A9B",
                applID: "CICS5A9B",
                netID: "USIBMT6",
                jobName: "CICS5A9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS1T9A-J90-CICSRegion": {
            id: "CICS1T9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1T9A-J90",
                name: "CICS1T9A",
                applID: "CICS1T9A",
                netID: "USIBMT6",
                jobName: "CICS1T9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS6A9C-J90-CICSRegion": {
            id: "CICS6A9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A9C-J90",
                name: "CICS6A9C",
                applID: "CICS6A9C",
                netID: "USIBMT6",
                jobName: "CICS6A9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS2A8D-J80-CICSRegion": {
            id: "CICS2A8D-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A8D-J80",
                name: "CICS2A8D",
                applID: "CICS2A8D",
                netID: "USIBMT6",
                jobName: "CICS2A8D",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "DBS1-J80-DB2Subsystem": {
            id: "DBS1-J80-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBS1",
                label: "DBS1-J80",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBS1",
                versionString: "13.1.0",
                scanDate: "2024-05-09T14:28:17Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBSG"
            }
        },
        "CICS3W8B-J80-CICSRegion": {
            id: "CICS3W8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3W8B-J80",
                name: "CICS3W8B",
                applID: "CICS3W8B",
                netID: "USIBMT6",
                jobName: "CICS3W8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS6T9A-J90-CICSRegion": {
            id: "CICS6T9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6T9A-J90",
                name: "CICS6T9A",
                applID: "CICS6T9A",
                netID: "USIBMT6",
                jobName: "CICS6T9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DBW5-J90-DB2Subsystem": {
            id: "DBW5-J90-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBW5",
                label: "DBW5-J90",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBW5",
                versionString: "13.1.0",
                scanDate: "2024-05-09T12:26:38Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBWG"
            }
        },
        "CICS6T8B-J80-CICSRegion": {
            id: "CICS6T8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6T8B-J80",
                name: "CICS6T8B",
                applID: "CICS6T8B",
                netID: "USIBMT6",
                jobName: "CICS6T8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2A9B-J90-CICSRegion": {
            id: "CICS2A9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2A9B-J90",
                name: "CICS2A9B",
                applID: "CICS2A9B",
                netID: "USIBMT6",
                jobName: "CICS2A9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5A8C-J80-CICSRegion": {
            id: "CICS5A8C-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A8C-J80",
                name: "CICS5A8C",
                applID: "CICS5A8C",
                netID: "USIBMT6",
                jobName: "CICS5A8C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS5A8A-J80-CICSRegion": {
            id: "CICS5A8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5A8A-J80",
                name: "CICS5A8A",
                applID: "CICS5A8A",
                netID: "USIBMT6",
                jobName: "CICS5A8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "J90-ZOS": {
            id: "J90-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "J90-UTCPLXJ8",
                name: "J90",
                smfId: "J90",
                netId: "USIBMT6",
                sscp: "PETJ90CP",
                fqdn: "j90.pok.stglabs.ibm.com",
                osName: "zOS",
                version: "03.01.00",
                sysResVolume: "PETPF0",
                iplParmDataset: "SYS0.IPLPARM",
                iplParmMember: "LOADPT",
                iplParmDevice: "5D01",
                iplParmVolume: "PETPF0",
                osFriendlyName: "LNKLSTJ2",
                osRsuLevel: "0",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS7AGA-J90-CICSRegion": {
            id: "CICS7AGA-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS7AGA-J90",
                name: "CICS7AGA",
                applID: "CICS7AGA",
                netID: "USIBMT6",
                jobName: "CICS7AGA",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DBA1-J90-DB2Subsystem": {
            id: "DBA1-J90-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBA1",
                label: "DBA1-J90",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBA1",
                versionString: "",
                scanDate: "2024-05-09T12:26:38Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBAG"
            }
        },
        "CICS3T9A-J90-CICSRegion": {
            id: "CICS3T9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3T9A-J90",
                name: "CICS3T9A",
                applID: "CICS3T9A",
                netID: "USIBMT6",
                jobName: "CICS3T9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS3A9C-J90-CICSRegion": {
            id: "CICS3A9C-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A9C-J90",
                name: "CICS3A9C",
                applID: "CICS3A9C",
                netID: "USIBMT6",
                jobName: "CICS3A9C",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DBWB-J90-DB2Subsystem": {
            id: "DBWB-J90-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DBWB",
                label: "DBWB-J90",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: "@DBWB",
                versionString: "13.1.0",
                scanDate: "2024-05-09T12:26:38Z",
                DDFLocation: "",
                "Data sharing group": "DSNDBWG"
            }
        },
        "CICS1A9A-J90-CICSRegion": {
            id: "CICS1A9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS1A9A-J90",
                name: "CICS1A9A",
                applID: "CICS1A9A",
                netID: "USIBMT6",
                jobName: "CICS1A9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS6A9A-J90-CICSRegion": {
            id: "CICS6A9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A9A-J90",
                name: "CICS6A9A",
                applID: "CICS6A9A",
                netID: "USIBMT6",
                jobName: "CICS6A9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS5T8A-J80-CICSRegion": {
            id: "CICS5T8A-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS5T8A-J80",
                name: "CICS5T8A",
                applID: "CICS5T8A",
                netID: "USIBMT6",
                jobName: "CICS5T8A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS6A8B-J80-CICSRegion": {
            id: "CICS6A8B-J80-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS6A8B-J80",
                name: "CICS6A8B",
                applID: "CICS6A8B",
                netID: "USIBMT6",
                jobName: "CICS6A8B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T14:28:17Z"
            }
        },
        "CICS2T9B-J90-CICSRegion": {
            id: "CICS2T9B-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS2T9B-J90",
                name: "CICS2T9B",
                applID: "CICS2T9B",
                netID: "USIBMT6",
                jobName: "CICS2T9B",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "QMQ5-J80-MQSubsystem": {
            id: "QMQ5-J80-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2024-05-09T14:28:17Z",
                label: "QMQ5-J80",
                name: "QMQ5",
                commandPrefixName: "!QMQ5",
                controllingAddressSpace: "QMQ5MSTR",
                versionString: ""
            }
        },
        "CICS7AGC-J90-CICSRegion": {
            id: "CICS7AGC-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS7AGC-J90",
                name: "CICS7AGC",
                applID: "CICS7AGC",
                netID: "USIBMT6",
                jobName: "CICS7AGC",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "DSNDBAG-UTCPLXJ8-DB2DataSharingGroup": {
            id: "DSNDBAG-UTCPLXJ8-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DSNDBAG-UTCPLXJ8",
                name: "DSNDBAG",
                groupAttachName: "DBAG",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2024-05-09T12:26:38Z"
            }
        },
        "CICS3A9A-J90-CICSRegion": {
            id: "CICS3A9A-J90-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICS3A9A-J90",
                name: "CICS3A9A",
                applID: "CICS3A9A",
                netID: "USIBMT6",
                jobName: "CICS3A9A",
                version: "6.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2024-05-09T12:26:38Z"
            }
        }
    },
    edges: [
        {
            relation: "contain",
            source: "UTCPLXJ8-Sysplex",
            target: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "UTCPLXJ8-Sysplex",
            target: "CSLPROD-IMSSysplexGroup"
        },
        {
            relation: "hasMember",
            source: "UTCPLXJ8-Sysplex",
            target: "J80-ZOS"
        },
        {
            relation: "hasMember",
            source: "UTCPLXJ8-Sysplex",
            target: "J90-ZOS"
        },
        {
            relation: "federates",
            source: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBW1-J80-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBWG-J80-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBWB-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIC-J90-CICSRegion",
            target: "DBWB-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIB-J90-CICSRegion",
            target: "DBWB-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIA-J90-CICSRegion",
            target: "DBWB-J90-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9C-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9B-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9A-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9B-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9A-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9B-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9A-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9C-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9C-J90-CICSRegion",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DBW5-J90-DB2Subsystem",
            target: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "uses",
            source: "DBWB-J90-DB2Subsystem",
            target: "DSNDBWG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "CSLPROD-IMSSysplexGroup",
            target: "IMS8-J80-IMSSubsystem"
        },
        {
            relation: "federates",
            source: "CSLPROD-IMSSysplexGroup",
            target: "IMS9-J90-IMSSubsystem"
        },
        {
            relation: "uses",
            source: "IMS9-J90-IMSSubsystem",
            target: "CSLPROD-IMSSysplexGroup"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "DBS1-J80-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBSG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBS1-J80-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DBS1-J80-DB2Subsystem",
            target: "DSNDBSG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS6T8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS5T8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS5A8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICSCT8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3T8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CSQ8-J80-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICSCA8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS6A8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS6A8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3A8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICSCA8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3W8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "DBW1-J80-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS1A8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS6T8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2T8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "MQ81-J80-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8E-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2T8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3A8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "DBWG-J80-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS1A8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8F-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICSWUI8-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS1T8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS1A8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3W8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "QMQ5-J80-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS5A8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2T8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3T8A-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "IMS8-J80-IMSSubsystem"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS2A8D-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS6A8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICSCA8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS3A8C-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J80-ZOS",
            target: "CICS5A8B-J80-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CSQ9-J90-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9A-J90-CICSRegion",
            target: "CICS2T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9B-J90-CICSRegion",
            target: "CICS2T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9C-J90-CICSRegion",
            target: "CICS2T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9A-J90-CICSRegion",
            target: "CICS2A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9A-J90-CICSRegion",
            target: "CICS2A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9A-J90-CICSRegion",
            target: "CICS2A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9C-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "DBD3-J90-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBDG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBD3-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS7AGC-J90-CICSRegion",
            target: "DBD3-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS7AGA-J90-CICSRegion",
            target: "DBD3-J90-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICS7AGB-J90-CICSRegion",
            target: "DBD3-J90-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DBD3-J90-DB2Subsystem",
            target: "DSNDBDG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2F9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9A-J90-CICSRegion",
            target: "CICS2F9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9B-J90-CICSRegion",
            target: "CICS2F9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9C-J90-CICSRegion",
            target: "CICS2F9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2F9A-J90-CICSRegion",
            target: "CICS2A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2F9A-J90-CICSRegion",
            target: "CICS2A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2F9A-J90-CICSRegion",
            target: "CICS2A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2T9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9C-J90-CICSRegion",
            target: "CICS2A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9C-J90-CICSRegion",
            target: "CICS2A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9C-J90-CICSRegion",
            target: "CICS2A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9A-J90-CICSRegion",
            target: "CICS2T9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9B-J90-CICSRegion",
            target: "CICS2T9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9C-J90-CICSRegion",
            target: "CICS2T9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "QMQ1-J90-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS3A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9C-J90-CICSRegion",
            target: "CICS3T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9C-J90-CICSRegion",
            target: "CICS3T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9A-J90-CICSRegion",
            target: "CICS3A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9B-J90-CICSRegion",
            target: "CICS3A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9B-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5A9A-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIC-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIB-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5AIA-J90-CICSRegion",
            target: "CICS5T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5AIC-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5AIB-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS5T9A-J90-CICSRegion",
            target: "CICS5AIA-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5AIC-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS1A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1T9A-J90-CICSRegion",
            target: "CICS1A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1A9A-J90-CICSRegion",
            target: "CICS1T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS1A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1T9A-J90-CICSRegion",
            target: "CICS1A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1A9B-J90-CICSRegion",
            target: "CICS1T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9A-J90-CICSRegion",
            target: "CICS2T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9B-J90-CICSRegion",
            target: "CICS2T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2A9C-J90-CICSRegion",
            target: "CICS2T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9B-J90-CICSRegion",
            target: "CICS2A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9B-J90-CICSRegion",
            target: "CICS2A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS2T9B-J90-CICSRegion",
            target: "CICS2A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "IMS9-J90-IMSSubsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS3A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9B-J90-CICSRegion",
            target: "CICS3T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9B-J90-CICSRegion",
            target: "CICS3T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9A-J90-CICSRegion",
            target: "CICS3A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9B-J90-CICSRegion",
            target: "CICS3A9B-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS6A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9A-J90-CICSRegion",
            target: "CICS6T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9A-J90-CICSRegion",
            target: "CICS6T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9B-J90-CICSRegion",
            target: "CICS6A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9A-J90-CICSRegion",
            target: "CICS6A9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS7AGC-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS1A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1T9A-J90-CICSRegion",
            target: "CICS1A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS1A9C-J90-CICSRegion",
            target: "CICS1T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "DBW5-J90-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2A9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS7AGA-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2A9B-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5AIA-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "DBA1-J90-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBAG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBA1-J90-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DBA1-J90-DB2Subsystem",
            target: "DSNDBAG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "DBWB-J90-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS1T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5A9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS3T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9A-J90-CICSRegion",
            target: "CICS3T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9B-J90-CICSRegion",
            target: "CICS3A9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS2A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5AIB-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS6T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9C-J90-CICSRegion",
            target: "CICS6T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9B-J90-CICSRegion",
            target: "CICS6T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9A-J90-CICSRegion",
            target: "CICS6A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9A-J90-CICSRegion",
            target: "CICS6A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "QMQ7-J90-MQSubsystem"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS6A9C-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9C-J90-CICSRegion",
            target: "CICS6T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9B-J90-CICSRegion",
            target: "CICS6A9C-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS3A9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3A9A-J90-CICSRegion",
            target: "CICS3T9A-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS3T9A-J90-CICSRegion",
            target: "CICS3A9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS6A9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6A9B-J90-CICSRegion",
            target: "CICS6T9B-J90-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICS6T9B-J90-CICSRegion",
            target: "CICS6A9B-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS3T9A-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "DBT1-J90-DB2Subsystem"
        },
        {
            relation: "federates",
            source: "DSNDBTG-UTCPLXJ8-DB2DataSharingGroup",
            target: "DBT1-J90-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DBT1-J90-DB2Subsystem",
            target: "DSNDBTG-UTCPLXJ8-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS6T9B-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS5A9B-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "J90-ZOS",
            target: "CICS7AGB-J90-CICSRegion"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DA1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DC1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "CSLIC1C-IMSSysplexGroup"
        },
        {
            relation: "hasMember",
            source: "LPAR400J-Sysplex",
            target: "SYSG-ZOS"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB1D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB1D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DA1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DA1D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DA1D-SYS-DB2Subsystem",
            target: "DA1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DC1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DC1E-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DC1E-SYS-DB2Subsystem",
            target: "DC1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "CSLIC1C-IMSSysplexGroup",
            target: "IC1C-SYS-IMSSubsystem"
        },
        {
            relation: "uses",
            source: "IC1C-SYS-IMSSubsystem",
            target: "CSLIC1C-IMSSysplexGroup"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DA1D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1Y-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA1-SYS-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWA1-SYS-CICSRegion",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1K-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1A-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1E-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWB1-SYS-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWB1-SYS-CICSRegion",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DA1X-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCB05-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSYX01-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWA2-SYS-CICSRegion",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCB07-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSYXG6-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1X-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1B-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCB08-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWB2-SYS-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWB2-SYS-CICSRegion",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "Q9G1-SYS-MQSubsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCB06-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA2-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "IC1C-SYS-IMSSubsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSAB51-SYS-CICSRegion"
        }
    ]
};
