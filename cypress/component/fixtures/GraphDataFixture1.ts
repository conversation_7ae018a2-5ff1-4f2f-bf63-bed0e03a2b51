export const GraphDataFixture1 = {
    label: "",
    nodes: {
        "DA1D-SYS-DB2Subsystem": {
            id: "DA1D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DA1D",
                label: "DA1D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DA1D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSYX01-SYS-CICSRegion": {
            id: "CICSYX01-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSYX01-SYS",
                name: "CICSYX01",
                applID: "CICSYX01",
                netID: "<PERSON><PERSON>O01",
                jobName: "CICSYX01",
                version: "5.5.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "IC1C-SYS-IMSSubsystem": {
            id: "IC1C-SYS-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2022-01-04T03:08:50Z",
                label: "IC1C-SYS",
                name: "IC1C",
                keyName: "AppServer",
                versionString: "12.1",
                commandPrefixName: "IC1C",
                controllingAddressSpace: "IMSC10CC",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "840597649",
                programsChecksum: "436537747",
                transactionsChecksum: "272056237"
            }
        },
        "CICSYXG6-SYS-CICSRegion": {
            id: "CICSYXG6-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSYXG6-SYS",
                name: "CICSYXG6",
                applID: "CICSYXG6",
                netID: "USCACO01",
                jobName: "CICSYXG6",
                version: "5.6.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "Q9G1-SYS-MQSubsystem": {
            id: "Q9G1-SYS-MQSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "MQSubsystem",
            metadata: {
                source: "DLA",
                scanDate: "2022-01-04T03:08:50Z",
                label: "Q9G1-SYS",
                name: "Q9G1",
                commandPrefixName: ">Q9G1",
                controllingAddressSpace: "Q9G1MSTR",
                versionString: "9.0.5"
            }
        },
        "CICSCWA2-SYS-CICSRegion": {
            id: "CICSCWA2-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA2-SYS",
                name: "CICSCWA2",
                applID: "CICSCWA2",
                netID: "USCACO01",
                jobName: "CICSCWA2",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA3-SYS-CICSRegion": {
            id: "CICSCWA3-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA3-SYS",
                name: "CICSCWA3",
                applID: "CICSCWA3",
                netID: "USCACO01",
                jobName: "CICSCWA3",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA4-SYS-CICSRegion": {
            id: "CICSCWA4-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA4-SYS",
                name: "CICSCWA4",
                applID: "CICSCWA4",
                netID: "USCACO01",
                jobName: "CICSCWA4",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA5-SYS-CICSRegion": {
            id: "CICSCWA5-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA5-SYS",
                name: "CICSCWA5",
                applID: "CICSCWA5",
                netID: "USCACO01",
                jobName: "CICSCWA5",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA6-SYS-CICSRegion": {
            id: "CICSCWA6-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA6-SYS",
                name: "CICSCWA6",
                applID: "CICSCWA6",
                netID: "USCACO01",
                jobName: "CICSCWA6",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA7-SYS-CICSRegion": {
            id: "CICSCWA7-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA7-SYS",
                name: "CICSCWA7",
                applID: "CICSCWA7",
                netID: "USCACO01",
                jobName: "CICSCWA7",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA8-SYS-CICSRegion": {
            id: "CICSCWA8-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA8-SYS",
                name: "CICSCWA8",
                applID: "CICSCWA8",
                netID: "USCACO01",
                jobName: "CICSCWA8",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA9-SYS-CICSRegion": {
            id: "CICSCWA9-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA9-SYS",
                name: "CICSCWA9",
                applID: "CICSCWA9",
                netID: "USCACO01",
                jobName: "CICSCWA9",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA10-SYS-CICSRegion": {
            id: "CICSCWA10-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA10-SYS",
                name: "CICSCWA10",
                applID: "CICSCWA10",
                netID: "USCACO01",
                jobName: "CICSCWA10",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DB1GRP-LPAR400J",
                name: "DB1GRP",
                groupAttachName: "DB1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWA1-SYS-CICSRegion": {
            id: "CICSCWA1-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWA1-SYS",
                name: "CICSCWA1",
                applID: "CICSCWA1",
                netID: "USCACO01",
                jobName: "CICSCWA1",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSAB51-SYS-CICSRegion": {
            id: "CICSAB51-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSAB51-SYS",
                name: "CICSAB51",
                applID: "CICSAB51",
                netID: "USCACO01",
                jobName: "CICSAB51",
                version: "5.1.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DC1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DC1GRP-LPAR400J",
                name: "DC1GRP",
                groupAttachName: "DC1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1B-SYS-DB2Subsystem": {
            id: "DB1B-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1B",
                label: "DB1B-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1B",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1V-SYS-DB2Subsystem": {
            id: "DC1V-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1V",
                label: "DC1V-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1V",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DA1X-SYS-DB2Subsystem": {
            id: "DA1X-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DA1X",
                label: "DA1X-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DA1X",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1W-SYS-DB2Subsystem": {
            id: "DC1W-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1W",
                label: "DC1W-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1W",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1D-SYS-DB2Subsystem": {
            id: "DB1D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1D",
                label: "DB1D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB2D-SYS-DB2Subsystem": {
            id: "DB2D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB2D",
                label: "DB2D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB2D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB3D-SYS-DB2Subsystem": {
            id: "DB3D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB3D",
                label: "DB3D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB3D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB4D-SYS-DB2Subsystem": {
            id: "DB4D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB4D",
                label: "DB4D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB4D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB5D-SYS-DB2Subsystem": {
            id: "DB5D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB5D",
                label: "DB5D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB5D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB6D-SYS-DB2Subsystem": {
            id: "DB6D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB6D",
                label: "DB6D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB6D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB7D-SYS-DB2Subsystem": {
            id: "DB7D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB7D",
                label: "DB7D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB7D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB8D-SYS-DB2Subsystem": {
            id: "DB8D-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB8D",
                label: "DB8D-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1D",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DA1GRP-LPAR400J-DB2DataSharingGroup": {
            id: "DA1GRP-LPAR400J-DB2DataSharingGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2DataSharingGroup",
            metadata: {
                source: "DLA",
                label: "DA1GRP-LPAR400J",
                name: "DA1GRP",
                groupAttachName: "DA1G",
                groupFunction: "DB2 Data Sharing",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWB2-SYS-CICSRegion": {
            id: "CICSCWB2-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWB2-SYS",
                name: "CICSCWB2",
                applID: "CICSCWB2",
                netID: "USCACO01",
                jobName: "CICSCWB2",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "SYSG-ZOS": {
            id: "SYSG-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "SYS-LPAR400J",
                name: "SYSG",
                smfId: "SYS",
                netId: "USCACO01",
                sscp: "CCCDRM25",
                fqdn: "wlag2.svl.ibm.com",
                osName: "zOS",
                version: "02.04.00",
                sysResVolume: "G2401C",
                iplParmDataset: "SYS2.IPLPARM",
                iplParmMember: "LOADGG",
                iplParmDevice: "8358",
                iplParmVolume: "G2401C",
                osFriendlyName: "LNKLST00",
                osRsuLevel: "0",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1Y-SYS-DB2Subsystem": {
            id: "DC1Y-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1Y",
                label: "DC1Y-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1Y",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DB1A-SYS-DB2Subsystem": {
            id: "DB1A-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DB1A",
                label: "DB1A-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DB1A",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1K-SYS-DB2Subsystem": {
            id: "DC1K-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1K",
                label: "DC1K-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1K",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "LPAR555X-Sysplex": {
            id: "LPAR555X-Sysplex",
            dataPlatform: "ZDiscovery",
            nodeType: "Sysplex",
            metadata: {
                source: "DLA",
                name: "LPAR555X",
                label: "LPAR555X",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "LPAR400J-Sysplex": {
            id: "LPAR400J-Sysplex",
            dataPlatform: "ZDiscovery",
            nodeType: "Sysplex",
            metadata: {
                source: "DLA",
                name: "LPAR400J",
                label: "LPAR400J",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCWB1-SYS-CICSRegion": {
            id: "CICSCWB1-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCWB1-SYS",
                name: "CICSCWB1",
                applID: "CICSCWB1",
                netID: "USCACO01",
                jobName: "CICSCWB1",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC5L-SYS-DB2Subsystem": {
            id: "DC5L-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC5L",
                label: "DC5L-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC5L",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1E-SYS-DB2Subsystem": {
            id: "DC1E-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1E",
                label: "DC1E-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1E",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1X-SYS-DB2Subsystem": {
            id: "DC1X-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1X",
                label: "DC1X-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1X",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC1Z-SYS-DB2Subsystem": {
            id: "DC1Z-SYS-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC1Z",
                label: "DC1Z-SYS",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC1Z",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "SYSF-ZOS": {
            id: "SYSF-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "SYSF-LPAR400J",
                name: "SYSF",
                smfId: "SYS",
                netId: "USCACO01",
                sscp: "CCCDRM25",
                fqdn: "wlag2.svl.ibm.com",
                osName: "zOS",
                version: "02.04.00",
                sysResVolume: "G2401C",
                iplParmDataset: "SYS2.IPLPARM",
                iplParmMember: "LOADGG",
                iplParmDevice: "8358",
                iplParmVolume: "G2401C",
                osFriendlyName: "LNKLST00",
                osRsuLevel: "0",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "J90-ZOS": {
            id: "J90-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "J90-LPAR400J",
                name: "J90",
                smfId: "SYS",
                netId: "USCACO01",
                sscp: "CCCDRM25",
                fqdn: "wlag2.svl.ibm.com",
                osName: "zOS",
                version: "02.04.00",
                sysResVolume: "G2401C",
                iplParmDataset: "SYS2.IPLPARM",
                iplParmMember: "LOADGG",
                iplParmDevice: "8358",
                iplParmVolume: "G2401C",
                osFriendlyName: "LNKLST00",
                osRsuLevel: "0",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "SYSL-ZOS": {
            id: "SYSL-ZOS",
            dataPlatform: "ZDiscovery",
            nodeType: "ZOS",
            metadata: {
                source: "DLA",
                label: "SYSL-LPAR555X",
                name: "SYSL",
                smfId: "SYS",
                netId: "USCACO01",
                sscp: "CCCDRM25",
                fqdn: "wlag2.svl.ibm.com",
                osName: "zOS",
                version: "02.04.00",
                sysResVolume: "G2401C",
                iplParmDataset: "SYS2.IPLPARM",
                iplParmMember: "LOADGG",
                iplParmDevice: "8358",
                iplParmVolume: "G2401C",
                osFriendlyName: "LNKLST00",
                osRsuLevel: "0",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "DC2A-SYSF-DB2Subsystem": {
            id: "DC2A-SYSF-DB2Subsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "DB2Subsystem",
            metadata: {
                source: "DLA",
                name: "DC2A",
                label: "DC2A-SYSF",
                keyName: "AppServer",
                modifier: "",
                release: "",
                commandPrefixName: ":DC2A",
                majorVersion: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "CICSCB05-SYS-CICSRegion": {
            id: "CICSCB05-SYS-CICSRegion",
            dataPlatform: "ZDiscovery",
            nodeType: "CICSRegion",
            metadata: {
                source: "DLA",
                label: "CICSCB05-SYS",
                name: "CICSCB05",
                applID: "CICSCB05",
                netID: "USCACO01",
                jobName: "CICSCB05",
                version: "5.4.0",
                dfhRegionDefaultUser: "",
                dfhRegionLogstream: "",
                dfhCICSHLQ: "DFH.V5R5M0",
                dfhRegionHLQ: "",
                dfhLEHLQ: "",
                dfhRegionCICSSvc: "",
                dfhCICSType: "",
                scanDate: "2022-01-04T03:08:50Z"
            }
        },
        "MCMPLEX1-Sysplex": {
            id: "MCMPLEX1-Sysplex",
            dataPlatform: "ZDiscovery",
            nodeLabel: "MCMPLEX1",
            nodeType: "Sysplex",
            metadata: {
                source: "DLA",
                name: "MCMPLEX1",
                label: "MCMPLEX1",
                scanDate: "2022-02-23T18:00:14Z"
            },
            nodeTypeLabel: "Sysplex",
            typeId: "sysplex"
        },
        "IF1B-LP11-IMSSubsystem": {
            id: "IF1B-LP11-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            nodeLabel: "IF1B",
            metadata: {
                source: "DLA",
                scanDate: "2022-02-23T18:00:14Z",
                label: "IF1B-LP11",
                name: "IF1B",
                keyName: "AppServer",
                versionString: "15.1",
                commandPrefixName: "IF1B",
                controllingAddressSpace: "IF1BCR",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "319469079",
                programsChecksum: "53915830",
                transactionsChecksum: "134461703"
            },
            nodeTypeLabel: "IMSSubsystem",
            typeId: "imssubsystem"
        },
        "IF1A-LP11-IMSSubsystem": {
            id: "IF1A-LP11-IMSSubsystem",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSubsystem",
            nodeLabel: "IF1A",
            metadata: {
                source: "DLA",
                scanDate: "2022-02-23T18:00:14Z",
                label: "IF1A-LP11",
                name: "IF1A",
                keyName: "AppServer",
                versionString: "15.1",
                commandPrefixName: "IF1A",
                controllingAddressSpace: "IF1ACR",
                IMSSubsysType: "DBDC",
                IMSPlexGroupName: "",
                IRLMGroupName: "",
                CQSGroupName: "",
                databasesChecksum: "319469079",
                programsChecksum: "53915830",
                transactionsChecksum: "134461703"
            },
            nodeTypeLabel: "IMSSubsystem",
            typeId: "imssubsystem"
        },
        "CSLIMSFA-IMSSysplexGroup": {
            id: "CSLIMSFA-IMSSysplexGroup",
            dataPlatform: "ZDiscovery",
            nodeType: "IMSSysplexGroup",
            nodeLabel: "CSLIMSFA",
            metadata: {
                source: "DLA",
                scanDate: "2022-02-23T18:00:14Z",
                label: "CSLIMSFA-MCMPLEX1",
                name: "CSLIMSFA",
                groupFunction: "IMSPlex"
            },
            nodeTypeLabel: "IMSSysplexGroup",
            typeId: "imssysplexgroup"
        }
    },
    edges: [
        {
            relation: "transactionalDependency",
            source: "CICSCWA1-SYS-CICSRegion",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA1-SYS-CICSRegion"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWB1-SYS-CICSRegion",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWB2-SYS-CICSRegion",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DA1D-SYS-DB2Subsystem",
            target: "DA1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DA1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DA1D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB1D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB1D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB2D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB2D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB3D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB3D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB4D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB4D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB5D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB5D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB6D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB6D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB7D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB7D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DB8D-SYS-DB2Subsystem",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DB1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DB8D-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DC5L-SYS-DB2Subsystem",
            target: "DC1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DC1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DC5L-SYS-DB2Subsystem"
        },
        {
            relation: "uses",
            source: "DC1E-SYS-DB2Subsystem",
            target: "DC1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "federates",
            source: "DC1GRP-LPAR400J-DB2DataSharingGroup",
            target: "DC1E-SYS-DB2Subsystem"
        },
        {
            relation: "transactionalDependency",
            source: "CICSCWA2-SYS-CICSRegion",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1V-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSAB51-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA2-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA3-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA4-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA5-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA6-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA7-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA8-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA9-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWA10-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWB1-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCB05-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSCWB2-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSYX01-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "CICSYXG6-SYS-CICSRegion"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DA1D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DA1X-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1A-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSF-ZOS",
            target: "DC2A-SYSF-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1B-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB1D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB2D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSF-ZOS",
            target: "DB3D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1Y-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1Z-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB4D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB5D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB6D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB7D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DB8D-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1E-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSL-ZOS",
            target: "DC5L-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1K-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1W-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1X-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "DC1Y-SYS-DB2Subsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "IC1C-SYS-IMSSubsystem"
        },
        {
            relation: "contain",
            source: "SYSG-ZOS",
            target: "Q9G1-SYS-MQSubsystem"
        },
        {
            relation: "hasMember",
            source: "LPAR555X-Sysplex",
            target: "SYSL-ZOS"
        },
        {
            relation: "hasMember",
            source: "LPAR400J-Sysplex",
            target: "SYSG-ZOS"
        },
        {
            relation: "hasMember",
            source: "LPAR400J-Sysplex",
            target: "SYSF-ZOS"
        },
        {
            relation: "hasMember",
            source: "LPAR400J-Sysplex",
            target: "J90-ZOS"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DA1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DB1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "DC1GRP-LPAR400J-DB2DataSharingGroup"
        },
        {
            relation: "contain",
            source: "MCMPLEX1-Sysplex",
            target: "CSLIMSFA-IMSSysplexGroup"
        },
        {
            relation: "federates",
            source: "CSLIMSFA-IMSSysplexGroup",
            target: "IF1B-LP11-IMSSubsystem"
        },
        {
            relation: "federates",
            source: "CSLIMSFA-IMSSysplexGroup",
            target: "IF1A-LP11-IMSSubsystem"
        },
        {
            relation: "uses",
            source: "IF1A-LP11-IMSSubsystem",
            target: "CSLIMSFA-IMSSysplexGroup"
        },
        {
            relation: "uses",
            source: "IF1B-LP11-IMSSubsystem",
            target: "CSLIMSFA-IMSSysplexGroup"
        },
        {
            relation: "contain",
            source: "LPAR400J-Sysplex",
            target: "CSLIMSFA-IMSSysplexGroup"
        }
    ]
};
