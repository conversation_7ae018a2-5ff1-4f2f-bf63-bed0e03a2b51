import {
    broadcastMessageToTopology,
    interceptResourceGroupSummaryApiCall,
    mountApp,
    nodeShouldExist,
    nodeShouldNotExist,
    resetData
} from "./Utils/testUtils";
import { FilterAction, MessageType, PostFilterMessage } from "../../src/common/messageTypes";
import { StatusConfig } from "../../src/utils/statusUtils";
import { CICSCWA6, LPAR400J } from "./Utils/FrequentTestNodes";

const onceGraphLoaded = () => {
    return nodeShouldExist(LPAR400J);
};

const openSearch = () => {
    return cy.get(`[data-testid="open-search-button"]`).should("exist").click();
};

const typeIntoSearch = (query: string) => {
    return cy.get(`[data-testid="search-combobox"]`).should("exist").type(query);
};

const clickResult = (resultName: string) => {
    return cy.get(`[data-testid="search-option-${resultName}"]`).should("exist").click();
};

const modalPressOk = () => {
    return cy
        .get(`[data-testid="properties-link-filter-modal-view"]`)
        .find(`button[class$="btn--primary"]`)
        .should("exist")
        .click();
};

describe("onDisableFilterCallback tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });
    it("Broadcast correct filter message with only changed buckets within a filter", () => {
        mountApp();
        onceGraphLoaded().then(() => {
            nodeShouldExist(CICSCWA6);
            const msg: PostFilterMessage = {
                type: MessageType.FILTER_DATA,
                action: FilterAction.Apply,
                data: {
                    subsystem: [],
                    resourceType: [{ content: "Db2", selected: true, value: "DB2" }],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 100 },
                    status: {
                        content: StatusConfig.All,
                        selected: true
                    }
                },
                id: "init"
            };
            broadcastMessageToTopology(msg);
            nodeShouldNotExist(CICSCWA6);
            openSearch().then(() => {
                typeIntoSearch("CICSCWA6");
                clickResult("CICSCWA6");
                cy.contains(
                    `CICSCWA6 is currently filtered by 1 filter ("CICS regions" filter). `
                ).should("exist");
                modalPressOk();
                cy.get("@postMessage").should((a: any) => {
                    // Second call since first is stopLoadingIndicator
                    const call = a.getCall(1);
                    const msg: PostFilterMessage = call.args[0];
                    const data = msg.data;
                    expect(data.resourceType).to.have.length(2);
                    expect(data.resourceType).to.deep.equal([
                        {
                            content: "Db2",
                            selected: true,
                            value: "DB2"
                        },
                        {
                            content: "CICS",
                            selected: true,
                            value: "CICS"
                        }
                    ]);
                    expect(data.subsystem).to.have.length(0);
                    expect(data.eventGroupSeverity).to.have.length(0);
                });
                nodeShouldExist(CICSCWA6);
            });
        });
    });
});
