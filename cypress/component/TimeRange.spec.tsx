import {
    resetData,
    assertDecoratorWithText,
    mountApp,
    openToolbarSettings,
    broadcastMessageToTopology,
    expandDSG,
    applyAllNodesToolbarSettings,
    interceptResourceGroupSummaryApiCall,
    nodeShouldNotExist,
    nodeShouldExist
} from "./Utils/testUtils";
import { MessageType, TimeSelectedMessage } from "../../src/common/messageTypes";
import { formatDateForBroadcastChannel } from "../../src/utils/dateUtils";
import { CICSAB51, DB1GRP } from "./Utils/FrequentTestNodes";

describe("Time range tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`should change selected radio button to All when using jump to node to disable unmonitored filter`, () => {
        mountApp();

        let msg: TimeSelectedMessage = {
            id: "mock",
            type: MessageType.TIME_SELECTED,
            timePickerSelection: {
                lastUsed: "",
                fromDate: formatDateForBroadcastChannel(new Date()),
                interval: "24h",
                toDate: formatDateForBroadcastChannel(new Date()),
                source: ""
            }
        };

        assertDecoratorWithText(DB1GRP, "Critical");
        expandDSG(DB1GRP);
        // Unmonitored node should not exist
        nodeShouldNotExist(CICSAB51).then(() => {
            // Broadcast time range change and the unmonitored nodes should still not exist
            broadcastMessageToTopology(msg).then(() => {
                // DSG should collapse after time change
                assertDecoratorWithText(DB1GRP, "Critical");
                nodeShouldNotExist(CICSAB51);

                // Set to show all nodes
                applyAllNodesToolbarSettings();

                // Unmonitored nodes should now exist
                nodeShouldExist(CICSAB51);

                assertDecoratorWithText(DB1GRP, "Critical");

                msg = {
                    id: "mock",
                    type: MessageType.TIME_SELECTED,
                    timePickerSelection: {
                        lastUsed: "",
                        fromDate: formatDateForBroadcastChannel(new Date()),
                        interval: "24h",
                        toDate: formatDateForBroadcastChannel(new Date()),
                        source: ""
                    }
                };

                expandDSG(DB1GRP).then(() => {
                    // Sending time range broadcast message should not hide unmonitored nodes
                    broadcastMessageToTopology(msg).then(() => {
                        assertDecoratorWithText(DB1GRP, "Critical");

                        nodeShouldExist(CICSAB51);
                        openToolbarSettings();
                        cy.get("[data-testid='monitored-radio-button']").should("be.visible");

                        cy.get("[data-testid='all-nodes-radio-button']").should("be.checked");
                    });
                });
            });
        });
    });
});
