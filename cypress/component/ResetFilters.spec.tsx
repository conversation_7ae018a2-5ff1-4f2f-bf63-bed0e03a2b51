import {
    mockIzoaDataWithoutFilterData,
    mountApp,
    resetData,
    assertNoDecorator,
    assertDecoratorWithText,
    broadcastMessageToTopology,
    interceptResourceGroupSummaryApiCall,
    nodeShouldExist,
    nodeShouldNotExist
} from "./Utils/testUtils";
import { StatusConfig, EnsembleStatusValues } from "../../src/utils/statusUtils";
import { ResetFilterMessage, MessageType } from "../../src/common/messageTypes";
import { CICSCWA3, DC1V, LPAR400J } from "./Utils/FrequentTestNodes";

describe("Reset Filters", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`reset all filters`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [{ content: "Db2", selected: true, value: "DB2" }],
                    eventGroupSeverity: [],
                    confidence: { from: 90, to: 95 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        // Replace dataProvider's getArtifacts with mockDataProvider's getArtifacts
        mountApp(newMockIzoaData);
        nodeShouldExist(DC1V);
        nodeShouldNotExist(CICSCWA3);
        assertNoDecorator(DC1V);
        nodeShouldExist(LPAR400J).then(() => {
            const msg: ResetFilterMessage = { type: MessageType.RESET_FILTER, id: "mock" };
            broadcastMessageToTopology(msg).then(() => {
                // DC1V (70 and Open) should have no decorator because confidence and status should
                // not be reset when filter is reset
                nodeShouldExist(DC1V);
                // CICSCWA3 (95 and Closed) should have decorator
                nodeShouldExist(CICSCWA3);
                assertNoDecorator(DC1V);
                assertDecoratorWithText(CICSCWA3, "Minor");
            });
        });
    });
});
