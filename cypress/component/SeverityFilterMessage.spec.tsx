import {
    EnsembleSeverityFilterValues,
    EnsembleFilterItem,
    getNumberFromEnsembleFilterValue
} from "../../src/common/ensembleFilterTypes";
import { PostFilterMessage, MessageType, FilterAction } from "../../src/common/messageTypes";
import {
    mockIzoaDataWithoutFilterData,
    toggleDSGSetting,
    resetData,
    assertDecoratorWithText,
    mountApp,
    broadcastMessageToTopology,
    interceptResourceGroupSummaryApiCall,
    nodeShouldExist,
    nodeShouldNotExist,
    testNodes,
    assertNoDecorator
} from "./Utils/testUtils";
import { StatusConfig, EnsembleStatusValues } from "../../src/utils/statusUtils";
import { DB1GRP, LPAR400J } from "./Utils/FrequentTestNodes";
import { EventGroupSeverity } from "../../src/utils/severityUtils";

export const getAssertsFromSeverity = (eventGroupSeverity: EventGroupSeverity) => {
    for (const severityNode of testNodes) {
        if (severityNode.expectedEventGroupSeverity === eventGroupSeverity) {
            if (
                severityNode.expectedEventGroupSeverity === EventGroupSeverity.NoTrendingEventGroup
            ) {
                nodeShouldExist(severityNode.id)
                    .find("[data-cy=decorator-container]")
                    .should("not.exist");
            } else {
                nodeShouldExist(severityNode.id)
                    .find("[data-cy=decorator-container]")
                    .contains(
                        EnsembleSeverityFilterValues[severityNode.expectedEventGroupSeverity]
                            ?.content ?? ""
                    );
            }
        } else {
            nodeShouldNotExist(severityNode.id);
        }
    }
};

describe("Severity filter message tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    Object.values(EnsembleSeverityFilterValues)
        .flatMap((ensembleSeverityFilterItem: EnsembleFilterItem | null) =>
            ensembleSeverityFilterItem ? [ensembleSeverityFilterItem] : []
        )
        .map((ensembleSeverityFilterItem: EnsembleFilterItem) => {
            // const ensembleSeverityFilterItem = {
            //     content: "Warning",
            //     selected: true,
            //     value: EventGroupSeverity.Warning
            // };
            it(`initialization with severity "${ensembleSeverityFilterItem.content}" filter and DSG toggle`, () => {
                const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
                newMockIzoaData.urlParams.push({
                    key: "eventData",
                    value: {
                        filter: {
                            subsystem: [],
                            resourceType: [],
                            eventGroupSeverity: [ensembleSeverityFilterItem],
                            confidence: { from: 70, to: 100 },
                            status: EnsembleStatusValues[StatusConfig.All]
                        }
                    }
                });

                mountApp(newMockIzoaData);
                if (ensembleSeverityFilterItem.value === EventGroupSeverity.NoTrendingEventGroup) {
                    assertNoDecorator(DB1GRP);
                } else {
                    assertDecoratorWithText(DB1GRP, ensembleSeverityFilterItem.content);
                }
                nodeShouldExist(LPAR400J).then(() => {
                    const eventGroupSeverity = getNumberFromEnsembleFilterValue(
                        ensembleSeverityFilterItem.value
                    );
                    if (eventGroupSeverity) {
                        nodeShouldExist(DB1GRP);
                        getAssertsFromSeverity(eventGroupSeverity);
                        toggleDSGSetting();
                        nodeShouldNotExist(DB1GRP);
                        getAssertsFromSeverity(eventGroupSeverity);
                    }
                });
            });
        });

    Object.values(EnsembleSeverityFilterValues)
        .flatMap((ensembleSeverityFilterItem: EnsembleFilterItem | null) =>
            ensembleSeverityFilterItem ? [ensembleSeverityFilterItem] : []
        )
        .map((ensembleSeverityFilterItem: EnsembleFilterItem) => {
            it(`broadcast channel sending ${ensembleSeverityFilterItem.content} filter and DSG toggle`, () => {
                mountApp();

                assertDecoratorWithText(DB1GRP, "Critical");
                nodeShouldExist(LPAR400J).then(() => {
                    const msg: PostFilterMessage = {
                        id: "mock",
                        type: MessageType.FILTER_DATA,
                        action: FilterAction.Apply,
                        data: {
                            subsystem: [],
                            resourceType: [],
                            eventGroupSeverity: [ensembleSeverityFilterItem],
                            confidence: { from: 70, to: 100 },
                            status: EnsembleStatusValues[StatusConfig.Open]
                        }
                    };

                    broadcastMessageToTopology(msg).then(() => {
                        const eventGroupSeverity = getNumberFromEnsembleFilterValue(
                            ensembleSeverityFilterItem.value
                        );
                        if (eventGroupSeverity) {
                            nodeShouldExist(DB1GRP);
                            getAssertsFromSeverity(eventGroupSeverity);

                            toggleDSGSetting();
                            nodeShouldNotExist(DB1GRP);

                            getAssertsFromSeverity(eventGroupSeverity);
                        }
                    });
                });
            });
        });
});
