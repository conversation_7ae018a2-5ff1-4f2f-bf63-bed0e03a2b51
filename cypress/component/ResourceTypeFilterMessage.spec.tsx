import { EnsembleFilterItem } from "../../src/common/ensembleFilterTypes";
import { getProperType } from "../../src/utils/typeUtils";
import {
    assertDecoratorWithText,
    broadcastMessageToTopology,
    interceptResourceGroupSummaryApiCall,
    mockIzoaDataWithoutFilterData,
    mountApp,
    nodeShouldExist,
    nodeShouldNotExist,
    resetData,
    testNodes,
    toggleDSGSetting
} from "./Utils/testUtils";
import { PostFilterMessage, MessageType, FilterAction } from "../../src/common/messageTypes";
import { StatusConfig, EnsembleStatusValues } from "../../src/utils/statusUtils";
import { CSLIMSFC, DB1GRP, LPAR400J } from "./Utils/FrequentTestNodes";

const getAssertsFromType = (type: string) => {
    for (const typeNode of testNodes) {
        if (typeNode.expectedType === type) {
            cy.get(`[data-testid='${typeNode.id}']`).should("exist");
        } else {
            cy.get(`[data-testid='${typeNode.id}']`).should("not.exist");
        }
    }
};
describe("ResourceType filter message tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    Object.values(getProperType)
        .flatMap((type) => (type.ensembleFilterType ? [type.ensembleFilterType] : []))
        .map((ensembleTypeFilterItem: EnsembleFilterItem) => {
            it(`initialization with type "${ensembleTypeFilterItem.content}" filter and DSG toggle`, () => {
                const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);

                newMockIzoaData.urlParams.push({
                    key: "eventData",
                    value: {
                        filter: {
                            subsystem: [],
                            resourceType: [ensembleTypeFilterItem],
                            eventGroupSeverity: [],
                            confidence: { from: 70, to: 100 },
                            status: EnsembleStatusValues[StatusConfig.Open]
                        }
                    }
                });

                mountApp(newMockIzoaData);

                nodeShouldExist(LPAR400J).then(() => {
                    if (typeof ensembleTypeFilterItem.value === "string") {
                        if (ensembleTypeFilterItem.value === "DB2") {
                            nodeShouldExist(DB1GRP);
                        } else {
                            nodeShouldNotExist(DB1GRP);
                        }
                        if (ensembleTypeFilterItem.value === "IMS") {
                            nodeShouldExist(CSLIMSFC);
                        } else {
                            nodeShouldNotExist(CSLIMSFC);
                        }
                        getAssertsFromType(ensembleTypeFilterItem.value);
                        toggleDSGSetting();
                        getAssertsFromType(ensembleTypeFilterItem.value);
                    }
                });
            });
        });

    Object.values(getProperType)
        .flatMap((type) => (type.ensembleFilterType ? [type.ensembleFilterType] : []))
        .map((ensembleTypeFilterItem: EnsembleFilterItem) => {
            it(`broadcast channel sending ${ensembleTypeFilterItem.content} filter and DSG toggle`, () => {
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [ensembleTypeFilterItem],
                        eventGroupSeverity: [],
                        confidence: { from: 70, to: 100 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                mountApp();

                assertDecoratorWithText(DB1GRP, "Critical");
                nodeShouldExist(LPAR400J).then(() => {
                    broadcastMessageToTopology(msg).then(() => {
                        if (typeof ensembleTypeFilterItem.value === "string") {
                            if (ensembleTypeFilterItem.value === "DB2") {
                                nodeShouldExist(DB1GRP);
                            } else {
                                nodeShouldNotExist(DB1GRP);
                            }
                            if (ensembleTypeFilterItem.value === "IMS") {
                                nodeShouldExist(CSLIMSFC);
                            } else {
                                nodeShouldNotExist(CSLIMSFC);
                            }
                            getAssertsFromType(ensembleTypeFilterItem.value);
                            toggleDSGSetting();
                            getAssertsFromType(ensembleTypeFilterItem.value);
                        }
                    });
                });
            });
        });
});
