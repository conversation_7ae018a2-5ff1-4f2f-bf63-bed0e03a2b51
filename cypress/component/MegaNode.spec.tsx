import { J90 } from "./Utils/FrequentTestNodes";
import {
    resetData,
    interceptResourceGroupSummaryApiCall,
    mountAppWithCustomGraphData,
    nodeShouldExist,
    applyAllNodesToolbarSettings
} from "./Utils/testUtils";
import { GraphDataMegaNodeFixture } from "./fixtures/GraphDataMegaNodeFixture";

describe("Mega node tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`should not have Cannot set properties of undefined (setting 'isUnmonitored') exception`, () => {
        mountAppWithCustomGraphData(GraphDataMegaNodeFixture);
        nodeShouldExist(J90);
        applyAllNodesToolbarSettings();
        cy.get('[data-nodeid="meganode:1"]').should("contain.text", "31 artifacts");
    });
});
