import { CICSAB51, SYSG } from "./Utils/FrequentTestNodes";
import {
    resetData,
    assertDecoratorWithText,
    mountApp,
    checkIfNodeIsFocused,
    clickPropertiesLink,
    cancelToolbarSettings,
    openToolbarSettings,
    interceptResourceGroupSummaryApiCall,
    nodeShouldNotExist,
    nodeShouldExist,
    closeSidebar
} from "./Utils/testUtils";

describe("Toolbar settings tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`should change selected radio button to All when using jump to node to disable unmonitored filter`, () => {
        mountApp();

        assertDecoratorWithText(SYSG, "Critical");
        nodeShouldNotExist(CICSAB51);

        // Topology setting should be default Monitored
        openToolbarSettings();
        cy.get("[data-testid='monitored-radio-button']").should("be.visible").should("be.checked");
        cancelToolbarSettings();

        nodeShouldExist(SYSG).click();

        checkIfNodeIsFocused(SYSG, "SYSG");
        clickPropertiesLink("CICSAB51", 2);
        cy.get(
            "[data-testid=properties-link-filter-modal-view] .bx--modal-footer .bx--btn--primary"
        ).click();
        // Broadcast message should be sent in onDisableFilterCallback after clicking Ok to disable filter
        cy.get("@postMessage").should("be.calledTwice");

        checkIfNodeIsFocused(CICSAB51, "CICSAB51");

        closeSidebar();
        openToolbarSettings();
        cy.get("[data-testid='all-nodes-radio-button']").should("be.visible").should("be.checked");
        cancelToolbarSettings();
    });
});
