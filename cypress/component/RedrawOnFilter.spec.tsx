import {
    applyToolbarSettings,
    interceptResourceGroupSummaryApiCall,
    mountAppWithCustomGraphData,
    openToolbarSettings,
    nodeShouldExist,
    nodeShouldNotExist,
    pathShouldExist,
    pathShouldNotExist,
    resetData,
    nodeShouldHaveCoords,
    toggleAllNodesButton,
    toggleMonitoredButton,
    toggleDataSharingGroup
} from "./Utils/testUtils";
import {
    DC1V,
    CICSCWA1,
    CICSCWA2,
    LPAR400J,
    DC2A,
    J90,
    DB1GRP,
    DB3D,
    SYSG,
    DC1X,
    DC1Z
} from "./Utils/FrequentTestNodes";
import { GraphDataFixture1 } from "./fixtures/GraphDataFixture1";

describe("Redraw on filter tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it("Should correctly redraw graph to be larger when nodes are added", () => {
        mountAppWithCustomGraphData(GraphDataFixture1);
        // Establish base coordinates for control nodes
        nodeShouldHaveCoords(DC1Z, 613, 112);
        nodeShouldHaveCoords(CICSCWA1, 613, 767);
        // Show all nodes, revealing CICSCWA2 which is unmonitored
        openToolbarSettings();
        toggleAllNodesButton();
        applyToolbarSettings();
        nodeShouldExist(CICSCWA2);
        // Check control nodes to see that they have moved to new expanded y coordinates
        nodeShouldHaveCoords(DC1Z, 620, 1479);
        nodeShouldHaveCoords(CICSCWA1, 620, 8);
    });

    it("Should correctly redraw graph to be smaller when nodes are removed", () => {
        mountAppWithCustomGraphData(GraphDataFixture1);
        nodeShouldExist(LPAR400J);
        // Show all nodes, revealing CICSCWA2 which is unmonitored.
        openToolbarSettings();
        toggleAllNodesButton();
        applyToolbarSettings();
        nodeShouldExist(CICSCWA2);
        // Check the two nodes to see that they are in their correct redraw places
        nodeShouldHaveCoords(DC1Z, 620, 1479);
        nodeShouldHaveCoords(CICSCWA1, 620, 8);
        // Show only monitored nodes removing CISCWA2
        openToolbarSettings();
        toggleMonitoredButton();
        applyToolbarSettings();
        nodeShouldNotExist(CICSCWA2);
        // Check the two nodes to see that they have contracted
        nodeShouldHaveCoords(DC1Z, 613, 112);
        nodeShouldHaveCoords(CICSCWA1, 613, 767);
    });

    it("Should render graph correctly upon redraw, without dangling lines", () => {
        mountAppWithCustomGraphData(GraphDataFixture1);
        nodeShouldHaveCoords(LPAR400J, 8, 424);
        nodeShouldHaveCoords(DC2A, 224, 8);
        nodeShouldHaveCoords(J90, 412, 346);

        nodeShouldHaveCoords(SYSG, 210, 532);
        // Path from SYSG to DB1GRP
        pathShouldExist("M548 746 L548 780");
        pathShouldExist("M564 730 L580 730");
        nodeShouldHaveCoords(DB1GRP, 412, 486);

        // Path from SYSG to DC1X
        pathShouldExist("M548 796 L820 796");
        nodeShouldHaveCoords(DC1X, 613, 532);

        pathShouldExist("M260 646 L260 780");
        pathShouldExist("M548 546 L548 614");
        pathShouldExist("M548 646 L548 714");
        pathShouldExist("M548 746 L548 780");
        pathShouldExist("M548 714 L548 746");

        const allNodesNoGroups = () => {
            openToolbarSettings();
            toggleAllNodesButton();
            toggleDataSharingGroup();
            applyToolbarSettings();
            nodeShouldExist(DB3D);
        };

        const monitoredNodesWithGroups = () => {
            openToolbarSettings();
            toggleMonitoredButton();
            toggleDataSharingGroup();
            applyToolbarSettings();
            nodeShouldExist(DC1V);
        };

        // Mess with both toolbar settings
        for (let i = 0; i < 5; i++) {
            allNodesNoGroups();
            monitoredNodesWithGroups();
        }
        allNodesNoGroups();

        // Mess with only the data sharing group settings
        for (let i = 0; i < 4; i++) {
            // make sure iterations are an even number
            openToolbarSettings();
            toggleDataSharingGroup();
            applyToolbarSettings();
        }

        // Dangling lines should not exist
        pathShouldNotExist("M548 716 L548 1416");
        pathShouldNotExist("M836 433 L836 1894");
        pathShouldNotExist("M1342 1416 L1342 1661");
        pathShouldNotExist("M1630 433 L1630 2237");
    });
});
