import { PostFilterMessage, MessageType, FilterAction } from "../../src/common/messageTypes";
import { EventGroupSeverity } from "../../src/utils/severityUtils";
import {
    mockIzoaDataWithoutFilterData,
    toggleDSGSetting,
    resetData,
    assertNoDecorator,
    mountApp,
    openDSGAndOpenGroupFilter,
    broadcastMessageToTopology,
    collapseDSG,
    interceptResourceGroupSummaryApiCall,
    expandDSG,
    nodeShouldExist,
    nodeShouldNotExist,
    assertDecoratorAndPropertiesDecorator,
    NoDecoratorType,
    assertNoDecoratorAndCheckPropertiesDecorator
} from "./Utils/testUtils";

import { EnsembleSeverityFilterValues } from "../../src/common/ensembleFilterTypes";
import { EnsembleStatusValues, StatusConfig } from "../../src/utils/statusUtils";
import { resourceGroupSummaryUrl } from "../../src/Constants";
import {
    C<PERSON>SCB05,
    <PERSON><PERSON><PERSON><PERSON>3,
    DB1GR<PERSON>,
    DB4D,
    DB5D,
    DB6D,
    DB7D,
    DB8D,
    DC1X,
    DC1Y,
    J90,
    LPAR400J,
    Q9G1,
    SYSG
} from "./Utils/FrequentTestNodes";

describe("Confidence filter message tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`initialize with Confidence: 94-95 Status: Open J90 has decorator CICSCWA3 no decorator then broadcast status to All and both should have decorators`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 94, to: 95 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(DC1Y, NoDecoratorType.NoTrending);

        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
            })
            .then(() => {
                // Set status to All so decorator appears on CICSCWA3
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 94, to: 95 },
                        status: EnsembleStatusValues[StatusConfig.All]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
                    toggleDSGSetting();
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
                });
            });
    });

    it(`initialize with Confidence: 94-95 Status: All J90 CICSCWA3 has decorator then broadcast status to Open and only J90 has decorator`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 94, to: 95 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
        assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
            })
            .then(() => {
                // Set status to Open so decorator disappears on CICSCWA3

                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 94, to: 95 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertNoDecoratorAndCheckPropertiesDecorator(
                        CICSCWA3,
                        NoDecoratorType.NoTrending
                    );
                    toggleDSGSetting();
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertNoDecoratorAndCheckPropertiesDecorator(
                        CICSCWA3,
                        NoDecoratorType.NoTrending
                    );
                });
            });
    });

    it(`initialize with Confidence: 70-94 Status: All J90 and CICSCWA3 no decorators, then broadcast confidence 95-96 and both has decorators`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 94 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
            })
            .then(() => {
                // Set confidence so decorator appears on J90 and CICSCWA3

                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 95, to: 96 },
                        status: EnsembleStatusValues[StatusConfig.All]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
                    toggleDSGSetting();
                    assertDecoratorAndPropertiesDecorator(J90, "Major", 95, true);
                    assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
                });
            });
    });

    it(`initialize with confidence 96-100 J90 CICSCWA3 should not have decorator `, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 96, to: 100 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        nodeShouldExist(LPAR400J).then(() => {
            toggleDSGSetting();
            nodeShouldNotExist(DB1GRP);
            assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
            assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        });
    });

    it(`initialize with confidence 70-78 DB1GRP should have Major decorator `, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 78 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Major", 78, true);
        nodeShouldExist(LPAR400J).then(() => {
            toggleDSGSetting();
            nodeShouldNotExist(DB1GRP);
            assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
            assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
            assertDecoratorAndPropertiesDecorator(DB5D, "Major", 78, true);
            toggleDSGSetting();

            assertDecoratorAndPropertiesDecorator(DB1GRP, "Major", 78, true);
            openDSGAndOpenGroupFilter(DB1GRP);

            cy.get("[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']")
                .contains("label", "Critical")
                .should("not.exist");
            cy.get(
                "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
            ).contains("label", "Major");
        });
    });

    // Test fails, see ITOA-18667
    // it(`initialize with confidence 70-79 DB1GRP should have Critical decorator and then decrease confidence score max by 1 multiple times, each time check decorator and group filter`, () => {
    //     const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
    //     newMockIzoaData.urlParams.push({
    //         key: "eventData",
    //         value: {
    //             filter: {
    //                 subsystem: [],
    //                 resourceType: [],
    //                 eventGroupSeverity: [],
    //                 confidence: { from: 70, to: 79 },
    //                 status: EnsembleStatusValues[StatusConfig.Open]
    //             }
    //         }
    //     });
    //     mountApp(newMockIzoaData);

    //     assertNoDecorator("J90-ZOS");
    //     assertNoDecorator("CICSCWA3-SYS-CICSRegion");
    //     assertDecoratorWithText("DB1GRP-LPAR400J-DB2DataSharingGroup", "Critical");
    //     cy.get("[data-testid='LPAR400J-Sysplex']")
    //         .should("exist")
    //         .then(() => {
    //             toggleDSGSetting();
    //             cy.get("[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup']").should("not.exist");
    //             assertNoDecorator("J90-ZOS");
    //             assertNoDecorator("CICSCWA3-SYS-CICSRegion");
    //             assertNoDecorator("DB4D-SYS-DB2Subsystem");
    //             assertDecoratorWithText("DB7D-SYS-DB2Subsystem", "Critical");
    //             toggleDSGSetting();
    //             cy.get("[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup']").should("exist");
    //         })
    //         .then(() => {
    //             const msg: PostFilterMessage = {
    //                 id: "mock",
    //                 type: MessageType.FILTER_DATA,
    //                 action: FilterAction.Apply,
    //                 data: {
    //                     subsystem: [],
    //                     resourceType: [],
    //                     eventGroupSeverity: [],
    //                     confidence: { from: 70, to: 78 },
    //                     status: EnsembleStatusValues[StatusConfig.Open]
    //                 }
    //             };

    //             broadcastMessageToTopology(msg).then(() => {
    //                 assertDecoratorWithText("DB1GRP-LPAR400J-DB2DataSharingGroup", "Major");
    //                 openDSGAndOpenGroupFilter("DB1GRP-LPAR400J-DB2DataSharingGroup");

    //                 cy.get(
    //                     "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                 )
    //                     .contains("label", "Critical")
    //                     .should("not.exist");
    //                 cy.get(
    //                     "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                 ).contains("label", "Major");

    //                 collapseDSG("DB1GRP-LPAR400J-DB2DataSharingGroup");
    //                 cy.then(() => {
    //                     msg.data.confidence = { from: 70, to: 77 };

    //                     broadcastMessageToTopology(msg).then(() => {
    //                         assertDecoratorWithText("DB1GRP-LPAR400J-DB2DataSharingGroup", "Minor");
    //                         openDSGAndOpenGroupFilter("DB1GRP-LPAR400J-DB2DataSharingGroup");

    //                         cy.get(
    //                             "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                         )
    //                             .contains("label", "Major")
    //                             .should("not.exist");
    //                         cy.get(
    //                             "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                         ).contains("label", "Minor");
    //                         collapseDSG("DB1GRP-LPAR400J-DB2DataSharingGroup");
    //                         cy.then(() => {
    //                             msg.data.confidence = { from: 70, to: 76 };

    //                             broadcastMessageToTopology(msg).then(() => {
    //                                 assertDecoratorWithText(
    //                                     "DB1GRP-LPAR400J-DB2DataSharingGroup",
    //                                     "Warning"
    //                                 );
    //                                 openDSGAndOpenGroupFilter(
    //                                     "DB1GRP-LPAR400J-DB2DataSharingGroup"
    //                                 );
    //                                 cy.get(
    //                                     "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                                 )
    //                                     .contains("label", "Minor")
    //                                     .should("not.exist");
    //                                 cy.get(
    //                                     "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
    //                                 ).contains("label", "Warning");
    //                                 collapseDSG("DB1GRP-LPAR400J-DB2DataSharingGroup");
    //                                 cy.then(() => {
    //                                     msg.data.confidence = { from: 70, to: 75 };

    //                                     broadcastMessageToTopology(msg).then(() => {
    //                                         assertNoDecorator(
    //                                             "DB1GRP-LPAR400J-DB2DataSharingGroup"
    //                                         );

    //                                         // Only has nodes with no trending event group left so filter button won't show up
    //                                         cy.get(
    //                                             `[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-filter']`
    //                                         ).should("not.exist");

    //                                         cy.get(`[data-testid='DB7D-SYS-DB2Subsystem']`).should(
    //                                             "not.exist"
    //                                         );
    //                                     });
    //                                 });
    //                             });
    //                         });
    //                     });
    //                 });
    //             });
    //         });
    // });

    it(`initialize with confidence 70-79 DB1GRP should have Critical decorator and then decrease confidence score by 1, check decorator and group filter`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 79 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Critical", 79, true);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(DB4D, NoDecoratorType.NoTrending);
                assertDecoratorAndPropertiesDecorator(DB7D, "Critical", 79, true);
                toggleDSGSetting();
                nodeShouldExist(DB1GRP);
            })
            .then(() => {
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 70, to: 78 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(DB1GRP, "Major", 78, true);
                    openDSGAndOpenGroupFilter(DB1GRP);

                    cy.get(
                        "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
                    )
                        .contains("label", "Critical")
                        .should("not.exist");
                    cy.get(
                        "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
                    ).contains("label", "Major");

                    collapseDSG("DB1GRP-LPAR400J-DB2DataSharingGroup");
                });
            });
    });

    it(`initialize with confidence 70-78 DB1GRP should have Major decorator and then decrease confidence score by 1, check decorator and group filter`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 78 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Major", 78, true);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(DB4D, NoDecoratorType.NoTrending);
                assertDecoratorAndPropertiesDecorator(DB5D, "Major", 78, true);
                toggleDSGSetting();
                nodeShouldExist(DB1GRP);
            })
            .then(() => {
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 70, to: 77 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(DB1GRP, "Minor", 77, true);
                    openDSGAndOpenGroupFilter(DB1GRP);

                    cy.get(
                        "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
                    ).then((groupFilter) => {
                        cy.wrap(groupFilter).contains("label", "Critical").should("not.exist");
                        cy.wrap(groupFilter).contains("label", "Major").should("not.exist");
                        cy.wrap(groupFilter).contains("label", "Minor").should("exist");
                    });

                    collapseDSG(DB1GRP);
                });
            });
    });

    it(`initialize with confidence 70-77 DB1GRP should have Minor decorator and then decrease confidence score by 1, check decorator and group filter`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 77 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Minor", 77, true);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(DB4D, NoDecoratorType.NoTrending);
                assertDecoratorAndPropertiesDecorator(DB6D, "Minor", 77, true);
                toggleDSGSetting();
                nodeShouldExist(DB1GRP);
            })
            .then(() => {
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 70, to: 76 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertDecoratorAndPropertiesDecorator(DB1GRP, "Warning", 76, true);
                    openDSGAndOpenGroupFilter(DB1GRP);

                    cy.get(
                        "[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-group-filter-by-severity']"
                    ).then((groupFilter) => {
                        cy.wrap(groupFilter).contains("label", "Critical").should("not.exist");
                        cy.wrap(groupFilter).contains("label", "Major").should("not.exist");
                        cy.wrap(groupFilter).contains("label", "Minor").should("not.exist");
                        cy.wrap(groupFilter).contains("label", "Warning").should("exist");
                    });
                    collapseDSG(DB1GRP);
                });
            });
    });

    it(`initialize with confidence 70-76 DB1GRP should have Warning decorator and then decrease confidence score by 1, check decorator and group filter`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 76 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Warning", 76, true);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertNoDecoratorAndCheckPropertiesDecorator(J90, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
                assertNoDecoratorAndCheckPropertiesDecorator(DB6D, NoDecoratorType.NoTrending);
                assertDecoratorAndPropertiesDecorator(DB8D, "Warning", 76, true);
                toggleDSGSetting();
                nodeShouldExist(DB1GRP);
            })
            .then(() => {
                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 70, to: 75 },
                        status: EnsembleStatusValues[StatusConfig.Open]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertNoDecorator(DB1GRP);
                    expandDSG(DB1GRP);

                    // Only has nodes with no trending event group left so filter button won't show up
                    cy.get(`[data-testid='DB1GRP-LPAR400J-DB2DataSharingGroup-filter']`).should(
                        "not.exist"
                    );
                    collapseDSG(DB1GRP);
                    nodeShouldNotExist(DB7D);
                });
            });
    });

    it(`should round confidence and cap at 100 CICSCB05 with confidence 69.5 rounded to 70 and DC1X with confidence 1000 set to 100`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 70, to: 100 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        mountApp(newMockIzoaData);

        assertDecoratorAndPropertiesDecorator(CICSCB05, "Warning", 70, false);
        assertDecoratorAndPropertiesDecorator(DC1X, "Warning", 100, true);
        nodeShouldExist(LPAR400J)
            .then(() => {
                toggleDSGSetting();
                nodeShouldNotExist(DB1GRP);
                assertDecoratorAndPropertiesDecorator(CICSCB05, "Warning", 70, false);
                assertDecoratorAndPropertiesDecorator(DC1X, "Warning", 100, true);
            })
            .then(() => {
                // Set confidence to 71-99 so decorators no longer show up

                const msg: PostFilterMessage = {
                    id: "mock",
                    type: MessageType.FILTER_DATA,
                    action: FilterAction.Apply,
                    data: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: { from: 71, to: 99 },
                        status: EnsembleStatusValues[StatusConfig.All]
                    }
                };

                broadcastMessageToTopology(msg).then(() => {
                    assertNoDecoratorAndCheckPropertiesDecorator(
                        CICSCB05,
                        NoDecoratorType.NoTrending
                    );
                    assertNoDecoratorAndCheckPropertiesDecorator(DC1X, NoDecoratorType.NoTrending);
                    toggleDSGSetting();
                    assertNoDecoratorAndCheckPropertiesDecorator(
                        CICSCB05,
                        NoDecoratorType.NoTrending
                    );
                    assertNoDecoratorAndCheckPropertiesDecorator(DC1X, NoDecoratorType.NoTrending);
                });
            });
    });

    it(`ITOA-16785 when critical severity is on, other severities shouldn't show up after updating confidence score`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        const mockGroupSummary = {
            data: [
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "ZOS",
                    resourceName: "SYSG",
                    resourceId: "ZOS_Subsystem_LPAR400J_SYSG_SYSG",
                    eventGroupSeverity: "6",
                    status: "open",
                    confidence: "70",
                    lastEventTimestamp: "2023-06-14 05:45:13.471"
                },
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "DB2",
                    resourceName: "DB4D",
                    resourceId: "DB2_Subsystem_LPAR400J_SYSG_DB4D",
                    eventGroupSeverity: "6",
                    status: "open",
                    confidence: "80",
                    lastEventTimestamp: "2023-06-14 15:17:44.553"
                },
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "MQ",
                    resourceName: "Q9G1",
                    resourceId: "MQ_Subsystem_LPAR400J_SYSG_Q9G1",
                    eventGroupSeverity: "4",
                    status: "open",
                    confidence: "70",
                    lastEventTimestamp: "2023-06-14 16:54:52.006"
                }
            ]
        };

        cy.intercept(`${resourceGroupSummaryUrl}*`, mockGroupSummary);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [
                        EnsembleSeverityFilterValues[EventGroupSeverity.Critical]!
                    ],
                    confidence: { from: 71, to: 100 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });
        mountApp(newMockIzoaData);
        nodeShouldNotExist(SYSG);
        nodeShouldNotExist(Q9G1);
        nodeShouldExist(LPAR400J).then(() => {
            const msg: PostFilterMessage = {
                id: "mock",
                type: MessageType.FILTER_DATA,
                action: FilterAction.Apply,
                data: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [
                        EnsembleSeverityFilterValues[EventGroupSeverity.Critical]!
                    ],
                    confidence: { from: 70, to: 100 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            };

            broadcastMessageToTopology(msg).then(() => {
                assertDecoratorAndPropertiesDecorator(SYSG, "Critical", 70, true);
                nodeShouldNotExist(Q9G1);
                // Only one more node should be visible after setting confidence from to 70
                cy.get(".node").should("have.length", 4);
            });
        });
    });

    it(`ITOA-16785 CICSCWA3 should not disappear if first Minor severity node appears`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        const mockGroupSummary = {
            data: [
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "CICS",
                    resourceName: "CICSCWA3",
                    resourceId: "CICS_Subsystem_LPAR400J_SYSG_CICSCWA3",
                    eventGroupSeverity: "4",
                    status: "closed",
                    confidence: "95",
                    lastEventTimestamp: "2023-06-14 22:30:34.411"
                }
            ]
        };

        cy.intercept(`${resourceGroupSummaryUrl}*`, mockGroupSummary);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 94, to: 95 },
                    status: EnsembleStatusValues[StatusConfig.Open]
                }
            }
        });

        mountApp(newMockIzoaData);
        assertNoDecoratorAndCheckPropertiesDecorator(CICSCWA3, NoDecoratorType.NoTrending);
        nodeShouldExist(LPAR400J).then(() => {
            const msg: PostFilterMessage = {
                id: "mock",
                type: MessageType.FILTER_DATA,
                action: FilterAction.Apply,
                data: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 94, to: 95 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            };

            broadcastMessageToTopology(msg).then(() => {
                assertDecoratorAndPropertiesDecorator(CICSCWA3, "Minor", 95, false);
            });
        });
    });

    it(`ITOA-19446 changing confidence/status causes groupsummary api call with new resource severity`, () => {
        const newMockIzoaData = structuredClone(mockIzoaDataWithoutFilterData);
        const mockGroupSummary = {
            data: [
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "ZOS",
                    resourceName: "SYSG",
                    resourceId: "ZOS_Subsystem_LPAR400J_SYSG_SYSG",
                    eventGroupSeverity: "6",
                    status: "closed",
                    confidence: "29.25",
                    lastEventTimestamp: "2023-06-14 05:45:13.471"
                },
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "DB2",
                    resourceName: "DB8D",
                    resourceId: "DB2_Subsystem_LPAR400J_SYSG_DB8D",
                    eventGroupSeverity: "4",
                    status: "open",
                    confidence: "76",
                    lastEventTimestamp: "2023-06-14 15:17:44.553"
                }
            ]
        };
        const mockGroupSummary2 = {
            data: [
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "ZOS",
                    resourceName: "SYSG",
                    resourceId: "ZOS_Subsystem_LPAR400J_SYSG_SYSG",
                    eventGroupSeverity: "5",
                    status: "closed",
                    confidence: "15.75",
                    lastEventTimestamp: "2023-06-14 05:45:13.471"
                },
                {
                    clusterName: "LPAR400J",
                    nodeName: "SYSG",
                    resourceType: "DB2",
                    resourceName: "DB8D",
                    resourceId: "DB2_Subsystem_LPAR400J_SYSG_DB8D",
                    eventGroupSeverity: "3",
                    status: "open",
                    confidence: "25",
                    lastEventTimestamp: "2023-06-14 15:17:44.553"
                }
            ]
        };

        cy.intercept(`${resourceGroupSummaryUrl}*`, mockGroupSummary);
        newMockIzoaData.urlParams.push({
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 0, to: 100 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            }
        });
        mountApp(newMockIzoaData);
        nodeShouldExist(SYSG);
        assertDecoratorAndPropertiesDecorator(SYSG, "Critical", 29, false);
        assertDecoratorAndPropertiesDecorator(DB1GRP, "Minor", 76, true);
        expandDSG(DB1GRP);
        assertDecoratorAndPropertiesDecorator(DB8D, "Minor", 76, true);
        collapseDSG(DB1GRP);
        cy.intercept(`${resourceGroupSummaryUrl}*`, mockGroupSummary2).as("groupSummaryCall");
        nodeShouldExist(LPAR400J).then(() => {
            const msg: PostFilterMessage = {
                id: "mock",
                type: MessageType.FILTER_DATA,
                action: FilterAction.Apply,
                data: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: { from: 0, to: 28 },
                    status: EnsembleStatusValues[StatusConfig.All]
                }
            };

            broadcastMessageToTopology(msg).then(() => {
                cy.wait("@groupSummaryCall");
                // Mouseover temp fix, proper fix: update ZVS version and use forceRedraw when updating decorators
                cy.get(`[data-testid='${SYSG}']`).trigger("mouseover");
                assertDecoratorAndPropertiesDecorator(SYSG, "Major", 16, false);
                assertDecoratorAndPropertiesDecorator(DB1GRP, "Warning", 25, true);
                expandDSG(DB1GRP);
                assertDecoratorAndPropertiesDecorator(DB8D, "Warning", 25, true);
            });
        });
    });
});
