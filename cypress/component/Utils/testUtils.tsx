import React from "react";
import { BroadcastChannel } from "broadcast-channel";
import { useFilterStore, useGraphStore, useZvsStore } from "@zvisualization/zvs";
import {
    dataProvider,
    izoaDashboardData,
    resetIzoaDashboardData
} from "../../../src/ZDSDataProvider";
import { EventGroupSeverity } from "../../../src/utils/severityUtils";
import { getProperType } from "../../../src/utils/typeUtils";
import { UrlParams } from "../../../src/common/types";
import { mockDataProvider } from "../../../src/mockdata/MockDataProvider";
import App, { topologyChannel } from "../../../src/App";
import { DefaultBroadcastChannelName, resourceGroupSummaryUrl } from "../../../src/Constants";
import useMonitorStore from "../../../src/store/monitor-store";
import { filterNodesAndEdges, processZDSGraphData } from "../../../src/utils/graphDataUtils";
import {
    PostFilterMessage,
    ResetFilterMessage,
    TimeSelectedMessage,
    MessageType
} from "../../../src/common/messageTypes";
import mockGroupSummaryData from "../../../src/mockdata/groupsummary.json";
import i18n from "../../../src/i18n";

export const mockIzoaDataWithoutFilterData: UrlParams = {
    urlParams: [
        { key: "id", value: "37412614-d215-49b1-98f1-1bafe8c9f377" },
        { key: "token", value: "123" },
        { key: "timestamp", value: "2021-03-27 03:50:00.0" },
        { key: "sysPlexName", value: "LPAR400J" },
        { key: "systemName", value: "SYS" },
        { key: "subsystem", value: "CICS" },
        { key: "subsystemId", value: "CICSCWA1" },
        { key: "mvsSystemId", value: "SYS" }
    ]
};

// Default filter data values sent from Ensemble
// (confidence default should be something else but this is currently what Ensemble sends)
export const mockIzoaDataWithDefaultFilterData: UrlParams = {
    urlParams: [
        ...mockIzoaDataWithoutFilterData.urlParams,
        {
            key: "eventData",
            value: {
                filter: {
                    subsystem: [],
                    resourceType: [],
                    eventGroupSeverity: [],
                    confidence: {
                        from: 0,
                        to: 100
                    },
                    status: {
                        content: "Open and closed",
                        selected: true,
                        value: "all"
                    }
                }
            }
        }
    ]
};

export const testNodes = [
    {
        id: "SYSG-ZOS",
        expectedEventGroupSeverity: EventGroupSeverity.Critical,
        expectedType: getProperType.zos.ensemble
    },
    {
        id: "CICSCWA1-SYS-CICSRegion",
        expectedEventGroupSeverity: EventGroupSeverity.Major,
        expectedType: getProperType.cicsregion.ensemble
    },
    {
        id: "Q9G1-SYS-MQSubsystem",
        expectedEventGroupSeverity: EventGroupSeverity.Minor,
        expectedType: getProperType.mqsubsystem.ensemble
    },
    {
        id: "DC1V-SYS-DB2Subsystem",
        expectedEventGroupSeverity: EventGroupSeverity.Warning,
        expectedType: getProperType.db2subsystem.ensemble
    },
    {
        id: "IC1C-SYS-IMSSubsystem",
        expectedEventGroupSeverity: EventGroupSeverity.NoTrendingEventGroup,
        expectedType: getProperType.imssubsystem.ensemble
    }
];

export enum NoDecoratorType {
    NoTrending,
    None
}

export const interceptResourceGroupSummaryApiCall = () => {
    cy.intercept(`${resourceGroupSummaryUrl}*`, mockGroupSummaryData);
};

const commonAppMount = (newMockIzoaData: UrlParams = mockIzoaDataWithDefaultFilterData) => {
    cy.spy(topologyChannel, "postMessage").as("postMessage");
    cy.mount(<App izoaData={newMockIzoaData} />);
    // postMessage should be called for initialization
    cy.get("@postMessage").should("be.calledOnce");
};

export const mountApp = (newMockIzoaData: UrlParams = mockIzoaDataWithDefaultFilterData) => {
    commonAppMount(newMockIzoaData);
    // Replace dataProvider's getArtifacts with mockDataProvider's getArtifacts
    cy.stub(dataProvider, "getArtifacts").callsFake(mockDataProvider.getArtifacts);
};

export const mountAppWithCustomGraphData = (
    graphData: any,
    newMockIzoaData: UrlParams = mockIzoaDataWithDefaultFilterData
) => {
    commonAppMount(newMockIzoaData);
    // Replace dataProvider's getArtifacts with mockDataProvider's getArtifacts
    const fakeGetArtifacts = () => {
        const data = structuredClone(graphData);
        filterNodesAndEdges(data, izoaDashboardData.showDataSharingGroups);
        return processZDSGraphData(data);
    };
    cy.stub(dataProvider, "getArtifacts").callsFake(fakeGetArtifacts);
};

export const toggleDSGSetting = () => {
    openToolbarSettings();
    toggleDataSharingGroup();
    applyToolbarSettings();
};

export const resetData = () => {
    useFilterStore.getState().resetFilter();
    useZvsStore.getState().setDefaultState();
    useGraphStore.getState().reset();
    useMonitorStore.getState().reset();
    resetIzoaDashboardData();
};

export const assertDecoratorWithText = (nodeId: string, content: string) => {
    nodeShouldExist(nodeId);
    cy.get(`[data-testid='${nodeId}'] > [data-cy=decorator-container]`).contains(content);
};

export const assertDecoratorAndPropertiesDecorator = (
    nodeId: string,
    content: string,
    confidence: number,
    open: boolean
) => {
    assertDecoratorWithText(nodeId, content);
    // Mouseover temp fix, proper fix: update ZVS version and use forceRedraw when updating decorators
    cy.get(`[data-testid='${nodeId}']`).trigger("mouseover");
    cy.get(`[data-testid='${nodeId}']`).click();
    checkPropertyValues(content, confidence, open);
    closeSidebar();
};

export const assertNoDecorator = (nodeId: string) => {
    nodeShouldExist(nodeId);
    cy.get(`[data-testid='${nodeId}'] > [data-cy=decorator-container]`).should("not.exist");
};

export const assertNoDecoratorAndCheckPropertiesDecorator = (
    nodeId: string,
    noDecoratorType: NoDecoratorType
) => {
    assertNoDecorator(nodeId);
    // Mouseover temp fix, proper fix: update ZVS version and use forceRedraw when updating decorators
    cy.get(`[data-testid='${nodeId}']`).trigger("mouseover");
    cy.get(`[data-testid='${nodeId}']`).click();
    if (noDecoratorType === NoDecoratorType.NoTrending) {
        cy.get(`[data-cy='event-group-property-data']`).contains(
            i18n.t("SEVERITY.NO_TRENDING_EVENT_GROUP")
        );
    } else if (noDecoratorType === NoDecoratorType.None) {
        cy.get(`[data-cy='event-group-property']`).should("not.exist");
    }
    closeSidebar();
};

export const expandDSG = (nodeId: string) => {
    nodeShouldExist(nodeId);
    cy.get(`[data-cy='${nodeId}-expand-btn']`).should("exist");
    cy.get(`[data-cy='${nodeId}-expand-btn']`).click();
    return cy.get(`[data-cy='${nodeId}-header-secondary-text']`).should("exist");
};
export const collapseDSG = (nodeId: string) => {
    cy.get(`[data-testid='${nodeId}-collapse-group']`).should("exist").click();
    nodeShouldExist(nodeId);
    cy.get(`[data-cy='${nodeId}-expand-btn']`).should("exist");
    return cy.get(`[data-cy='${nodeId}-header-secondary-text']`).should("not.exist");
};

export const openDSGAndOpenGroupFilter = (nodeId: string) => {
    expandDSG(nodeId);
    cy.get(`[data-testid='${nodeId}-filter']`).click();
};
export const clickPropertiesLink = (label: string, propertiesSectionIndex: number) => {
    cy.get("[data-cy=properties-section-data]")
        .eq(propertiesSectionIndex)
        .find("[data-cy=properties-link]")
        .contains(label)
        .click();
};

export const checkIfNodeIsFocused = (nodeId: string, label: string) => {
    nodeShouldExist(nodeId);
    cy.get("[data-testid=properties-panel-container]").should("be.visible");
    cy.get("[data-testid=properties-panel-container] > div").first().should("contain", label);
    cy.get(`[data-testid="${nodeId}"]`).should("have.css", "outline-style", "solid");
};

export const openToolbarSettings = () => {
    cy.get("[data-testid='toolbar-settings']").should("be.visible").click("left");
    cy.get("[data-testid='toolbar-settings-popover-title']").should("be.visible");
};

export const applyAllNodesToolbarSettings = () => {
    openToolbarSettings();
    cy.get("[data-testid='monitored-radio-button']").should("be.visible").should("be.checked");
    cy.get("[data-testid='all-nodes-radio-button']").siblings("label").first().click();
    applyToolbarSettings();
};

export const applyMonitoredNodesToolbarSettings = () => {
    openToolbarSettings();
    cy.get("[data-testid='all-nodes-radio-button']").should("be.visible").should("be.checked");
    cy.get("[data-testid='monitored-radio-button']").siblings("label").first().click();
    applyToolbarSettings();
};

export const cancelToolbarSettings = () => {
    cy.get("[data-testid='cancel-toolbar-settings']").click();
    cy.get("[data-testid='toolbar-settings-popover-title']").should("not.be.visible");
};

export const applyToolbarSettings = () => {
    cy.get("[data-testid='apply-toolbar-settings']").click();
    cy.get("[data-testid='toolbar-settings-popover-title']").should("not.be.visible");
};

export const broadcastMessageToTopology = (
    msg: PostFilterMessage | ResetFilterMessage | TimeSelectedMessage
) => {
    const ensembleSenderAndReceiver = new BroadcastChannel(DefaultBroadcastChannelName);
    const args: any[] = [msg.type];
    if (msg.type === MessageType.FILTER_DATA) {
        args.push(JSON.stringify(msg.data));
    } else if (msg.type === MessageType.TIME_SELECTED) {
        args.push(JSON.stringify(msg.timePickerSelection));
    }

    cy.log("Sending broadcast message", ...args);
    return ensembleSenderAndReceiver.postMessage(msg);
};

export const toggleNodeCheckbox = (nodeId: string) => {
    cy.get(`[data-testid='${nodeId}'] [data-cy='node-manifest-checkbox']`).should("exist");
    cy.get(`[data-testid='${nodeId}'] [data-cy='node-manifest-checkbox']`).click();
};

export const nodeShouldBeSelected = (nodeId: string) => {
    cy.get(
        `[data-testid='${nodeId}'] [data-cy='node-manifest-checkbox'] [data-cy='node-selected-icon']`
    ).should("exist");
};

export const nodeShouldNotBeSelected = (nodeId: string) => {
    cy.get(
        `[data-testid='${nodeId}'] [data-cy='node-manifest-checkbox'] [data-cy='node-not-selected-icon']`
    ).should("exist");
};

export const nodeShouldBePartiallySelected = (nodeId: string) => {
    cy.get(
        `[data-testid='${nodeId}'] [data-cy='node-manifest-checkbox'] [data-cy='node-partially-selected-icon']`
    ).should("exist");
};

export const pathShouldExist = (d: string) => {
    return cy.get(`path[d="${d}"]`).should("exist");
};

export const pathShouldNotExist = (d: string) => {
    return cy.get(`path[d="${d}"]`).should("not.exist");
};

export const nodeShouldExist = (nodeId: string) => {
    return cy.get(`[data-testid=${nodeId}]`).should("exist");
};

export const nodeShouldNotExist = (nodeId: string) => {
    return cy.get(`[data-testid=${nodeId}]`).should("not.exist");
};

export const nodeShouldHaveCoords = (nodeId: string, x: number, y: number) => {
    cy.get(`[data-testid=${nodeId}]`)
        .should("exist")
        .should("have.attr", "x", `${x}`)
        .should("have.attr", "y", `${y}`);
};

export const toggleMonitoredButton = () => {
    return cy.get(`label[for="monitored-radio-button"]`).should("exist").click();
};

export const toggleAllNodesButton = () => {
    return cy.get(`label[for="all-nodes-radio-button"]`).should("exist").click();
};

export const toggleDataSharingGroup = () => {
    return cy.get("[data-testid='sharing-group-toggle']").siblings().click();
};

export const closeSidebar = () => {
    cy.get("[data-cy=close-sidebar]").filter(":visible").click();
    cy.get("[data-cy=close-sidebar]").should("not.be.visible");
};

export const checkPropertyValues = (name: string, confidence: number, open: boolean) => {
    const groupName = '[data-cy="event-group-property-data__icon-name-confidence__name"';
    const confidenceText = '[data-cy="event-group-property-data__icon-name-confidence__confidence"';
    const propertyLabel = '[data-cy="event-group-property__toggletip-label"]';
    const icon = '[data-cy="event-group-property-data__icon-name-confidence__icon"]';
    const statusTag = '[data-cy="event-group-property-data__status"]';

    cy.get(propertyLabel).contains("Event group overview").should("exist");
    cy.get(icon).should("exist");
    cy.get(groupName).contains(name).should("exist");
    cy.get(confidenceText).contains(confidence).should("exist");
    if (open) {
        cy.get(statusTag).should("not.exist");
    } else {
        cy.get(statusTag).contains("Closed").should("exist");
    }
};
