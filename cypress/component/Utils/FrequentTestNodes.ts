export const CICSAB51 = "CICSAB51-SYS-CICSRegion";
export const CICSCWA1 = "CICSCWA1-SYS-CICSRegion";
export const CICSCWA2 = "CICSCWA2-SYS-CICSRegion";
export const CICSCWA3 = "CICSCWA3-SYS-CICSRegion";
export const CICSCWA6 = "CICSCWA6-SYS-CICSRegion";
export const CSLIMSFC = "CSLIMSFC-IMSSysplexGroup";
export const DB1GRP = "DB1GRP-LPAR400J-DB2DataSharingGroup";
export const DB2D = "DB2D-SYS-DB2Subsystem";
export const DB3D = "DB3D-SYS-DB2Subsystem";
export const DB4D = "DB4D-SYS-DB2Subsystem";
export const DB5D = "DB5D-SYS-DB2Subsystem";
export const DB6D = "DB6D-SYS-DB2Subsystem";
export const DB7D = "DB7D-SYS-DB2Subsystem";
export const DB8D = "DB8D-SYS-DB2Subsystem";
export const DC1V = "DC1V-SYS-DB2Subsystem";
export const DC1X = "DC1X-SYS-DB2Subsystem";
export const DC1Y = "DC1Y-SYS-DB2Subsystem";
export const DC1Z = "DC1Z-SYS-DB2Subsystem";
export const DC2A = "DC2A-SYSF-DB2Subsystem";
export const IF1B = "IF1B-SYS-IMSSubsystem";
export const J90 = "J90-ZOS";
export const LPAR400J = "LPAR400J-Sysplex";
export const Q9G1 = "Q9G1-SYS-MQSubsystem";
export const SYSG = "SYSG-ZOS";
export const CICSCB05 = "CICSCB05-SYS-CICSRegion";
