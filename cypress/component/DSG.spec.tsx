import { CSLIMSFC, DB1GRP, DB4D, IF1B } from "./Utils/FrequentTestNodes";
import {
    resetData,
    mountApp,
    interceptResourceGroupSummaryApiCall,
    pathShouldExist,
    assertDecoratorWithText,
    expandDSG,
    toggleDSGSetting,
    nodeShouldExist,
    nodeShouldNotExist,
    mountAppWithCustomGraphData
} from "./Utils/testUtils";
import { GraphDataFixture1 } from "./fixtures/GraphDataFixture1";

describe("Data sharing group tests", () => {
    beforeEach(() => {
        resetData();
        interceptResourceGroupSummaryApiCall();
    });

    it(`Node should be moved out of group and should exist and have edges to DSG`, () => {
        mountAppWithCustomGraphData(GraphDataFixture1);
        expandDSG(DB1GRP);
        cy.get("[data-testid='DB4D-SYS-DB2Subsystem'] [data-cy='node-action-menu'")
            .should("exist")
            .click();
        cy.get(".bx--overflow-menu-options__option")
            .eq(1)
            .should("contain", "Move out of group")
            .click();

        nodeShouldExist(DB4D);

        // Edge from DB4D
        pathShouldExist("M1316 200 L1316 296");
        // Edge going into DB1GRP after DB4D is moved out
        pathShouldExist("M1284 545 L1300 545");
    });

    it(`DB2 and IMS DSG should show or hide depending on the toggle setting`, () => {
        mountApp();
        expandDSG(DB1GRP);
        assertDecoratorWithText(DB4D, "Critical");
        expandDSG(CSLIMSFC);
        assertDecoratorWithText(IF1B, "Warning");

        toggleDSGSetting();

        nodeShouldNotExist(DB1GRP);
        assertDecoratorWithText(DB4D, "Critical");

        nodeShouldNotExist(CSLIMSFC);
        assertDecoratorWithText(IF1B, "Warning");

        toggleDSGSetting();
        assertDecoratorWithText(DB1GRP, "Critical");
        assertDecoratorWithText(CSLIMSFC, "Minor");
    });
});
