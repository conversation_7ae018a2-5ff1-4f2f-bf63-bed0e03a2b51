import path from "path";
import HtmlWebpackPlugin from "html-webpack-plugin";
import ForkTsCheckerWebpackPlugin from "fork-ts-checker-webpack-plugin";
import ESLintPlugin from "eslint-webpack-plugin";
const ModuleFederationPlugin = require("webpack/lib/container/ModuleFederationPlugin");
const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

module.exports = {
    mode: "development",
    devtool: "inline-source-map",
    devServer: {
        static: {
            directory: path.join(__dirname, "build")
        },
        historyApiFallback: true,
        port: 4001,
        open: true,
        hot: true,
        proxy: [
            {
                context: ["/zrdds"],
                target: "https://9.46.236.100:8888",
                changeOrigin: true,
                secure: false
            }
        ]
    },
    entry: "./src/index.tsx",
    output: {
        publicPath: "auto",
        uniqueName: "zdiscovery"
    },
    module: {
        rules: [
            {
                test: /\.(ts|js)x?$/i,
                exclude: /node_modules/,
                use: {
                    loader: "babel-loader",
                    options: {
                        presets: [
                            "@babel/preset-env",
                            "@babel/preset-react",
                            "@babel/preset-typescript"
                        ]
                    }
                }
            },
            {
                test: /\.s[ac]ss$/i,
                use: [
                    // Creates `style` nodes from JS strings
                    "style-loader",
                    // Translates CSS into CommonJS
                    "css-loader",
                    // Compiles Sass to CSS
                    "sass-loader"
                ]
            }
        ]
    },
    resolve: {
        extensions: [".tsx", ".ts", ".js"]
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: "src/index.html"
        }),
        new ForkTsCheckerWebpackPlugin({
            async: false
        }),
        new ESLintPlugin({
            extensions: ["js", "jsx", "ts", "tsx"]
        }),
        new ModuleFederationPlugin({
            name: "zdiscovery",
            library: { type: "var", name: "zdiscovery" },
            filename: "remoteEntry.js",
            exposes: {
                "./web-components": "./src/App.tsx"
            },
            shared: ["react", "react-dom"]
        }),
        new BundleAnalyzerPlugin()
    ]
};
