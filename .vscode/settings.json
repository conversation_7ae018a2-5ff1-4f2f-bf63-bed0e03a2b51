{"gitlens.remotes": [{"domain": "github.ibm.com", "type": "GitHub"}], "editor.formatOnSave": true, "github-enterprise.uri": "https://github.ibm.com", "i18n-ally.localesPaths": ["src/Locales"], "editor.defaultFormatter": "esbenp.prettier-vscode", "cSpell.words": ["CICS", "CICSAB", "CICSCB", "CICSCWA", "cicsregion", "CISCWA", "CSLIMSFC", "datasharinggroup", "graphmenubar", "groupsummary", "imssubsystem", "imssysplexgroup", "insightpack", "ITOA", "izaa", "izoa", "Keycloak", "meganode", "mockdata", "mqsubsystem", "samwise", "SYSF", "SYSG", "sysplex", "sysplexes", "testct", "testid", "zdiscovery", "zrdds", "zustand", "zvisualization"], "[markdown]": {"editor.defaultFormatter": "DavidAnson.vscode-markdownlint"}}