const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");
const ESLintPlugin = require("eslint-webpack-plugin");
// @ts-ignore
const ModuleFederationPlugin = require("webpack/lib/container/ModuleFederationPlugin");
const Dotenv = require("dotenv-webpack");
const packageJson = require("./package.json");
const deps = packageJson.dependencies;

module.exports = {
    mode: "development",
    devtool: "inline-source-map",
    devServer: {
        static: {
            directory: path.join(__dirname, "build")
        },
        historyApiFallback: true,
        port: 4000,
        open: true,
        hot: true,
        proxy: [
            {
                context: ["/zrdds"],
                target: "https://9.46.236.100:8888",
                changeOrigin: true,
                secure: false
            }
        ],
        client: {
            overlay: {
                warnings: false
            }
        }
    },
    entry: "./src/index.tsx",
    output: {
        publicPath: "auto",
        uniqueName: "zdiscovery"
    },
    module: {
        rules: [
            {
                test: /\.(ts|js)x?$/i,
                exclude: /node_modules/,
                use: {
                    loader: "babel-loader",
                    options: {
                        presets: [
                            "@babel/preset-env",
                            "@babel/preset-react",
                            "@babel/preset-typescript"
                        ]
                    }
                }
            },
            {
                test: /\.s[ac]ss$/i,
                use: [
                    // Creates `style` nodes from JS strings
                    "style-loader",
                    // Translates CSS into CommonJS
                    "css-loader",
                    // Compiles Sass to CSS
                    "sass-loader"
                ]
            }
        ]
    },
    resolve: {
        extensions: [".tsx", ".ts", ".js"]
    },
    plugins: [
        new Dotenv({
            path: "./.env.development"
        }),
        new HtmlWebpackPlugin({
            template: "src/index.html"
        }),
        new ForkTsCheckerWebpackPlugin({
            async: false
        }),
        new ESLintPlugin({
            extensions: ["js", "jsx", "ts", "tsx"]
        }),
        new ModuleFederationPlugin({
            name: "zdiscovery",
            library: { type: "var", name: "zdiscovery" },
            filename: "remoteEntry.js",
            exposes: {
                "./web-components": "./src/App.tsx"
            },
            shared: {
                react: {
                    singleton: true,
                    requiredVersion: deps.react
                },
                "react-dom": {
                    singleton: true,
                    requiredVersion: deps["react-dom"]
                }
            }
        })
    ]
};
