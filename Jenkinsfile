// Using Scripted Pipeline for maximum flexibility

String gitBranch = "${env.BRANCH_NAME}"
String buildId = "${env.BUILD_NUMBER}"
String version = "511"
String fix = "dev"
if (gitBranch ==~ /.*_release$/) {
  version = gitBranch.substring(0,5).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, '$1.$2.$3')
  fix = gitBranch.substring(0,5).replaceAll(/([0-9])([0-9])([0-9])([0-9])/, '$4')
}
if (gitBranch.equals("service_v5.1.0")) {
  fix = "10"
}
String gitOrg = "IZOA"
String gitRepo = "zaa-ensemble-zdiscovery"
String ensembleDockerRepo = "zaa-common-docker-MF"
String nodeVersion = "v16.13.2"
String afPath = "sys-izoa-generic-local/zaa/components/"
boolean branchExists = false

def nowDate = new Date()
final buildDate = nowDate.format('yyyyMMdd-HHmm')

def checkBranch = { org, repo ->
  queryString = 'git ls-remote --heads ******************:' + org + '/' + repo + ' ' + gitBranch + '| awk \'{ print $2 }\''
  String branchTest = sh(
          script: queryString,
          returnStdout: true
  )
  if (branchTest.trim().endsWith(gitBranch)) {
    branchExists = true
  }
  return branchExists
}

def lastSuccessfulBuild(passedBuilds, build) {
  if ((build != null) && (build.result != 'SUCCESS')) {
    passedBuilds.add(build)
    lastSuccessfulBuild(passedBuilds, build.getPreviousBuild())
  }
}

@NonCPS
def getChangeLog(passedBuilds) {
  def log = ""
  for (int x = 0; x < passedBuilds.size(); x++) {
    def currentBuild = passedBuilds[x];
    def changeLogSets = currentBuild.changeSets
    for (int i = 0; i < changeLogSets.size(); i++) {
      def entries = changeLogSets[i].items
      for (int j = 0; j < entries.length; j++) {
        def entry = entries[j]
        def date = new Date(entry.timestamp)
        def sdf = date.format("yyyy-MM-dd hh:mm:ss").toString()
        log += "* ${entry.commitId} ${entry.msg} (${entry.author} @ ${sdf}) \n"
      }
    }
  }
  return log;
}

node('docker-x86_64') {
  withEnv(["GITBRANCH=" + gitBranch,
    "BUILD_DATE=" + buildDate,
    "BUILD_ID=" + buildId,
    "ORG=" + gitOrg,
    "REPO=" + gitRepo,
    "DOCKER_REPO=" + ensembleDockerRepo,
    "VRM=" + version,
    "FIX=" + fix,
    "NODEJS_VERSION=" + nodeVersion,
    "AF_PATH=" + afPath
  ]) {
    String targetDir = 'GITWORK/' + gitRepo
    sh(script: 'rm -Rf ' + targetDir + '&& mkdir -p ' + targetDir, returnStdout: true)
    sh(script: 'rm -Rf DIST && mkdir -p DIST', returnStdout: true)
    checkout([
            $class: 'GitSCM',
            branches: [[name: 'refs/heads/' + gitBranch]],
            extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: targetDir]],
            userRemoteConfigs: [[url: '******************:' + gitOrg + '/' + gitRepo + '.git']]
    ])
    def ensembleDockerBranchExists = checkBranch(gitOrg, ensembleDockerRepo)
    passedBuilds = []
    lastSuccessfulBuild(passedBuilds, currentBuild);
    echo "Number of reviewed builds: " + passedBuilds.size()
    echo "List of reviewed builds:"
    for (int i = 0; i < passedBuilds.size(); i++) {
      echo "    #" + passedBuilds[i].number
    }
    def changeLog = getChangeLog(passedBuilds)
    echo "Change log since last successful build:"
    echo "---------------------------------------"
    echo "\'${changeLog}\'"
    // First build cannot determine a 'last successful' build and must always run to establish a baseline
    if ((changeLog == '') && (currentBuild.number > 1)) {
      echo "No changes found; will not run build."
      currentBuild.result = 'NOT_BUILT'
    } else {
      stage('Build Z Topology UI') {
        try {
          withCredentials([
                  usernamePassword(credentialsId: 'izoagsa',
                          passwordVariable: 'GSAPWD',
                          usernameVariable: 'GSAUSER'),
                  usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                          passwordVariable: 'artifactoryToken',
                          usernameVariable: 'artifactoryUser')
          ]) {
            sh '''
              cd GITWORK/${REPO}
              # Need NodeJS for this build to work
              if [ -d ${HOME}/.nvm/versions/node/${NODEJS_VERSION} ]
              then
                export PATH=${HOME}/.nvm/versions/node/${NODEJS_VERSION}/bin:${PATH}
                node --version
              else
                echo "ERROR: Correct NodeJS version not found."
                exit 1
              fi
              # Get Artifactory authentication
              npm config set @zvisualization:registry https://eu.artifactory.swg-devops.com/artifactory/api/npm/sys-nazare-cicd-team-ui-npm-virtual
              echo "registry=https://na.artifactory.swg-devops.com/artifactory/api/npm/sys-izoa-npm-virtual/" >> .npmrc
              curl -u ${artifactoryUser}:${artifactoryToken} https://eu.artifactory.swg-devops.com/artifactory/api/npm/sys-nazare-cicd-team-ui-npm-virtual/auth/zvisualization >> .npmrc 
              curl -u ${artifactoryUser}:${artifactoryToken} https://na.artifactory.swg-devops.com/artifactory/api/npm/auth/ >> .npmrc
              if [ -f ${HOME}/.npmrc ]
              then
                mv ${HOME}/.npmrc ${HOME}/.npmrc.${BUILD_DATE}
              fi
              cp .npmrc ${HOME}/.npmrc
              # Run actual build
              npm install
              # Checking the version of the ZVS UI installed from artifactory
              npm list
              npm run build
              # npm run test
              npm ls --prod --json --depth=0 > ${REPO}.json
              rm -f ${HOME}/.npmrc && mv ${HOME}/.npmrc.${BUILD_DATE} ${HOME}/.npmrc
            '''
            /* TODO: Re-enable this and the last line in ssh stanza above once tests are fixed
            publishHTML(target: [reportDir  : 'GITWORK/izoa-insights-common-ui-MF/_tests/coverage/',
                                 reportFiles: 'index.html',
                                 reportName : 'IZOA Insights Common UI Coverage Report'])
            junit testResults: 'GITWORK/izoa-insights-common-ui-MF/_tests/junit/junit.xml',
                    skipPublishingChecks: true

            */
          }
        }
        catch (ignored) {
          unstable(message: "'${STAGE_NAME}' failed.")
          currentBuild.result = 'FAILURE'
        }
      }
      stage('Push Z Topology UI build output to Metric ML Docker repo') {
        if (ensembleDockerBranchExists) {
          try {
            sh '''
              THISDIR=`pwd`
              DOCKER_DIR="${THISDIR}/TMP/DOCKER"
              rm -Rf ${DOCKER_DIR}
              mkdir -p ${DOCKER_DIR}
              cd ${DOCKER_DIR}
              <NAME_EMAIL>:${ORG}/${DOCKER_REPO}.git -b ${GITBRANCH}
              mkdir -p ${DOCKER_REPO}/libs
              cd ${THISDIR}/GITWORK/${REPO}/build
              if [ -f ${DOCKER_DIR}/${DOCKER_REPO}/libs/ensemble-topology-ui-MF.tar.gz ]
              then
                rm -f ${DOCKER_DIR}/${DOCKER_REPO}/libs/ensemble-topology-ui-MF.tar.gz
              fi
              tar --numeric-owner --owner=0 --group=0 -cvzf ${DOCKER_DIR}/${DOCKER_REPO}/libs/ensemble-topology-ui-MF.tar.gz *
              cd ${DOCKER_DIR}/${DOCKER_REPO}
              git config user.email "<EMAIL>"
              git config user.name "IZOA Build"
              git add libs/ensemble-topology-ui-MF.tar.gz
              git commit -m "${REPO} build ID: ${BUILD_ID}; date: ${BUILD_DATE}"
              git fetch && git pull --commit --no-edit
              git push
            '''
          }
          catch (ignored) {
            unstable(message: "'${STAGE_NAME}' failed.")
            currentBuild.result = 'FAILURE'
          }
        } else {
          echo ensembleDockerRepo + ' branch ' + gitBranch + ' does not exist.'
          echo 'Will not update ' + ensembleDockerRepo + ' with build output from ' + gitRepo + '.'
        }
      }
      stage('Create Z Topology insight pack') {
        try {
          sh '''
              zip -j DIST/ensemble-topology-MF.zip GITWORK/${REPO}/config/*
            '''
        }
        catch (ignored) {
          unstable(message: "'${STAGE_NAME}' failed.")
          currentBuild.result = 'FAILURE'
        }
      }
      stage('Publish Z Topology insight pack') {
        try {
          sh '''
              cd DIST
              jf rt upload ensemble-topology-MF.zip ${AF_PATH}/v${VRM}-${FIX}/${GITBRANCH}/ensemble-topology/
            '''
        }
        catch (ignored) {
          unstable(message: "'${STAGE_NAME}' failed.")
          currentBuild.result = 'FAILURE'
        }
      }
    }
  }
}
