apiVersion: tekton.dev/v1beta1
kind: EventListener
metadata:
    name: github-listener
spec:
    triggers:
        - binding:
              name: trigger-binding
          template:
              name: trigger-template
---
apiVersion: tekton.dev/v1beta1
kind: TriggerBinding
metadata:
    name: trigger-binding
spec:
    params:
        - name: repository
          value: "$(event.pull_request.base.repo.clone_url)"
        - name: branch
          value: "$(event.pull_request.base.ref)"
        - name: pr-repository
          value: "$(event.pull_request.head.repo.clone_url)"
        - name: pr-branch
          value: "$(event.pull_request.head.ref)"
        - name: pr-revision
          value: "$(event.pull_request.head.sha)"
        - name: triggerName
          value: "github-pullrequest"

---
apiVersion: tekton.dev/v1beta1
kind: TriggerTemplate
metadata:
    name: trigger-template
spec:
    params:
        - name: repository
          description: The git repo
        - name: branch
          description: the branch for the git repo
        - name: pr-repository
          description: The source git repo for the PullRequest
        - name: pr-branch
          description: The source branch for the PullRequest
        - name: pr-revision
          description: the commit id/sha for the PullRequest
    resourcetemplates:
        - apiVersion: v1
          kind: PersistentVolumeClaim
          metadata:
              name: pipelinerun-$(uid)-pvc
          spec:
              resources:
                  requests:
                      storage: 1Gi
              volumeMode: Filesystem
              accessModes:
                  - ReadWriteOnce
        - apiVersion: tekton.dev/v1beta1
          kind: PipelineRun
          metadata:
              name: pipelinerun-$(uid)
          spec:
              pipelineRef:
                  name: pipeline-workspace
              params:
                  - name: repository
                    value: $(params.repository)
                  - name: branch
                    value: $(params.branch)
                  - name: pr-repository
                    value: $(params.pr-repository)
                  - name: pr-branch
                    value: $(params.pr-branch)
                  - name: pr-revision
                    value: $(params.pr-revision)
              workspaces:
                  - name: pipeline-workspace
                    persistentVolumeClaim:
                        claimName: pipelinerun-$(uid)-pvc

---
apiVersion: tekton.dev/v1beta1
kind: Pipeline
metadata:
    name: pipeline-workspace
spec:
    workspaces:
        - name: pipeline-workspace
    params:
        - name: repository
        - name: branch
        - name: pr-repository
          description: The source git repo for the PullRequest
          default: ""
        - name: pr-branch
          description: The source branch for the PullRequest
          default: ""
        - name: pr-revision
          description: the commit id/sha for the PullRequest
          default: ""
        - name: context
          default: "Ensemble build and test"
        - name: description
          default: "Tekton"
    tasks:
        - name: set-git-commit-pending
          taskRef:
              name: git-set-commit-status
          workspaces:
              - name: artifacts
                workspace: pipeline-workspace
          params:
              - name: repository
                value: $(params.repository)
              - name: revision
                value: $(params.pr-revision)
              - name: context
                value: $(params.context)
              - name: description
                value: $(params.description)
              - name: state
                value: "pending"
        - name: clone
          workspaces:
              - name: output
                workspace: pipeline-workspace
          taskRef:
              name: git-clone-repo
          params:
              - name: repository
                value: $(params.repository)
              - name: branch
                value: $(params.branch)
              - name: pr-repository
                value: $(params.pr-repository)
              - name: pr-branch
                value: $(params.pr-branch)
              - name: pr-revision
                value: $(params.pr-revision)
        - name: npm-install-build-test
          workspaces:
              - name: output
                workspace: pipeline-workspace
          taskRef:
              name: task-install-build-test
          runAfter:
              - clone
    finally:
        - name: set-git-commit-status
          taskRef:
              name: git-set-commit-status
          workspaces:
              - name: artifacts
                workspace: pipeline-workspace
          params:
              - name: repository
                value: $(params.repository)
              - name: revision
                value: $(params.pr-revision)
              - name: context
                value: $(params.context)
              - name: description
                value: $(params.description)
              - name: state
                value: "$(tasks.status)"

---
apiVersion: tekton.dev/v1beta1
kind: Task
metadata:
    name: task-install-build-test
spec:
    workspaces:
        - name: output
    steps:
        - name: npm-install-build-test
          image: cypress/base:20.12.2
          envFrom:
              - configMapRef:
                    name: environment-properties
              - secretRef:
                    name: secure-properties
          script: |
              cd $(workspaces.output.path)
              CYPRESS_CACHE_FOLDER=$(workspaces.output.path)/tmp/cypress
              npm config set //na.artifactory.swg-devops.com/artifactory/api/npm/sys-izoa-npm-virtual/:_authToken $artifactorytoken -location project
              npm config set //eu.artifactory.swg-devops.com/artifactory/api/npm/sys-nazare-cicd-team-ui-npm-virtual/:_authToken $artifactorytoken -location project
              npm i
              npm run build
              npm run testct:headless
