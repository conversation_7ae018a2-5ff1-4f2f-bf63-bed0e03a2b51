{"name": "zdiscovery", "version": "0.0.1", "scripts": {"start": "webpack serve --config webpack.dev.config.ts", "analyze": "webpack serve --config webpack.analyze.config.ts", "build": "webpack --config webpack.prod.config.ts", "eslint": "eslint ./ --ext .tsx", "testct": "cypress open --component --browser chrome", "testct:headless": "cypress run --headless --component"}, "proxy": "http://zoastack1.fyre.ibm.com:8888", "dependencies": {"@carbon/layout": "11.24.0", "@carbon/react": "1.64.0", "@zvisualization/zvs": "3.0.8-dev.202407030043", "broadcast-channel": "^7.1.0", "carbon-components": "10.58.9", "react": "17.0.2", "react-dom": "17.0.2", "react-i18next": "14.1.3", "rxjs": "6.6.7", "styled-components": "6.1.12", "zustand": "4.5.4"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/plugin-transform-runtime": "7.26.10", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@babel/preset-typescript": "7.26.0", "@babel/runtime": "7.26.10", "@types/carbon__colors": "10.31.3", "@types/carbon__layout": "0.0.3", "@types/react": "17.0.80", "@types/react-dom": "17.0.25", "@typescript-eslint/eslint-plugin": "8.1.0", "@typescript-eslint/parser": "8.1.0", "babel-loader": "10.0.0", "clean-webpack-plugin": "4.0.0", "cors": "2.8.5", "css-loader": "7.1.2", "cypress": "13.13.3", "dotenv-webpack": "8.1.0", "eslint": "8.57.0", "eslint-plugin-cypress": "3.5.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "4.6.2", "eslint-webpack-plugin": "4.2.0", "fork-ts-checker-webpack-plugin": "9.0.2", "html-webpack-plugin": "5.6.0", "sass": "1.77.6", "sass-loader": "16.0.0", "style-loader": "4.0.0", "ts-node": "10.9.2", "typescript": "5.5.4", "webpack": "5.94.0", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "5.1.4", "webpack-dev-server": "5.0.4"}, "overrides": {"jspdf": "3.0.1", "dompurify": "3.2.6"}}