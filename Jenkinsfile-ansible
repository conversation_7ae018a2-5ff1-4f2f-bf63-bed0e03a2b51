node('zatcntl1_ansible') {
    try {
        stage ('Kick off inventory file Setup if host doest not exist'){
            String[] host_list
            host_list = "${host}".split(',')
            for (item in host_list) {
                grep = 'grep -Fqi ' + item +  '  /home/<USER>/izoa/inventory'
                String result = sh( script: grep, returnStatus:true )
                if (result != "0")
                    build job: 'Setup-Ansible-User', wait: true,  parameters: [
                        string(name: 'host', value: "${item}"), password(name: 'root_password', value: "${root_password}")]
                else
                    echo "Host exists on the inventory file"
        }
    }
        stage('Place Playbooks in the System')
                        {
                                script
                                {
                                    ws('/home/<USER>/zdiscovery_github/')
                                    {
                                        checkout([$class: 'GitSCM', branches: [[name: '*/develop']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: '71efbb2b-652b-4cc0-9ffe-05cc4c877f2d', url: '******************:IZOA/zaa-ensemble-zdiscovery.git']]])
                                    }
                                }
                        }
        stage('Ansible Kickoff'){
          withCredentials([
                  usernamePassword(credentialsId: 'IZOA_Functional_Artifactory',
                          passwordVariable: 'artifactoryToken',
                          usernameVariable: 'artifactoryUser')
          ])
                {
                  sh '''
                            cd /home/<USER>/zdiscovery_github/
                            ansible-playbook ansible/ansible.yml -vv --extra-vars "branch=${branch} host=${host} artifactoryUser=${artifactoryUser} artifactoryToken=${artifactoryToken}" -i /home/<USER>/izoa/inventory
                     '''
                }
        }
    }
    catch (ignored) {
        currentBuild.result = 'FAILURE'
    }
}
