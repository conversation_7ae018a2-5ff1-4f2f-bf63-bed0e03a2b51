{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["@typescript-eslint", "react-hooks"], "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "rules": {"react-hooks/rules-of-hooks": "error", "react/prop-types": "off", "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/explicit-module-boundary-types": "off"}, "settings": {"react": {"pragma": "React", "version": "detect"}}}