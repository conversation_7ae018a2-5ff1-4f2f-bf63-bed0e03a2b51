/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2022, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

export function getCredentialsHeader(izoaDashboardData: any) {
    let headers: HeadersInit = { "Content-Type": "application/json" };
    if (process.env.IS_LOCAL_DEV === "true") {
        headers = {
            ...headers,
            ...{
                ApiToken: process.env.STATIC_TOKEN ?? ""
            }
        };
    } else if (izoaDashboardData.getKeycloakToken) {
        izoaDashboardData.getKeycloakToken().subscribe((keycloakTokenResponse: string) => {
            if (keycloakTokenResponse) {
                headers = {
                    ...headers,
                    ...{
                        Authorization: `Bearer ${keycloakTokenResponse}`
                    }
                };
            }
        });
    }
    return headers;
}
