/* eslint-disable @typescript-eslint/no-unused-vars */
/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2022, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { izoaDashboardData } from "../ZDSDataProvider";
import { getCredentialsHeader } from "./auth.service";
import data from "../mockdata/groupsummary.json";
import { ApiResource, Resource } from "../common/types";
import { resourceGroupSummaryUrl } from "../Constants";

/* Get data to populate table on scorecard dashboard */
export const getResourceGroupSummary = async (
    from: string,
    to: string,
    confidenceFrom: number,
    confidenceTo: number,
    state?: string,
    clusterName?: string,
    nodeName?: string,
    resourceType?: string
): Promise<Resource[]> => {
    let resourcesWithStringSeverity = [];
    if (process.env.IS_LOCAL_DEV === "true") {
        resourcesWithStringSeverity = data?.data as any;
    } else {
        const headers = getCredentialsHeader(izoaDashboardData);
        const params = { from, to, confidenceFrom, confidenceTo, state };
        const responseData = await fetch(resourceGroupSummaryUrl, {
            method: "POST",
            credentials: "include",
            headers: headers,
            body: JSON.stringify(params)
        })
            .then((response) => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error(response.statusText);
            })
            .catch((error) => {
                console.warn(error);
                return null;
            });

        resourcesWithStringSeverity = responseData?.data as ApiResource[];
    }

    const newResources = convertResourcesStringSeverityAndConfidenceToNumber(
        resourcesWithStringSeverity
    );

    // Round confidence number and set max to 100 to match how its handled in Ensemble
    return newResources.map((resource) => {
        resource.confidence = resource.confidence > 100 ? 100 : Math.round(resource.confidence);
        return resource;
    });
};

const convertResourcesStringSeverityAndConfidenceToNumber = (
    resourcesWithStringSeverity: ApiResource[]
): Resource[] => {
    const newResources = [];
    if (resourcesWithStringSeverity && resourcesWithStringSeverity.length > 0) {
        for (const res of resourcesWithStringSeverity) {
            newResources.push({
                ...res,
                eventGroupSeverity: Number(res.eventGroupSeverity),
                confidence: Number(res.confidence ?? 0)
            });
        }
    }
    return newResources;
};
