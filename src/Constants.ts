/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { EnsembleData } from "./common/types";
import { StatusConfig, EnsembleStatusValues } from "./utils/statusUtils";
import { PostFilterMessage, MessageType, FilterAction } from "./common/messageTypes";

export const ZDS_CACHE_TIME = 90; // the time to cache zrdds data in minutes
export const DefaultBroadcastChannelName = "zaa_channel";
export const DefaultBroadcastMessageId = "TOPOLOGYML";
export const resourceGroupSummaryUrl = "/ensembleapi/resource/groupsummary";

export enum MonitorState {
    ALL = "all",
    MONITORED = "monitored"
}

export const DefaultConfidenceScoreConfig = { from: 70, to: 100 };
export const DefaultStatusConfig = EnsembleStatusValues[StatusConfig.Open];

export const DefaultIzoaDashboardData: EnsembleData = {
    resourceGroupSummary: [],

    // IZAA callback to get valid keycloak token for verifying against ZDS APIs
    getKeycloakToken: null,

    showDataSharingGroups: true
};

export const resetMessage: PostFilterMessage = {
    type: MessageType.FILTER_DATA,
    action: FilterAction.Apply,
    id: "mock",
    data: {
        subsystem: [],
        resourceType: [],
        confidence: null,
        status: null,
        eventGroupSeverity: []
    }
};
export const CustomElementId = "zdiscovery-element";
