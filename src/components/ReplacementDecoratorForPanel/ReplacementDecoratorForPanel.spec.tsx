import React from "react";
import "./../../App.scss";
import { ReplacementDecoratorForPanel } from "./ReplacementDecoratorForPanel";
import { ELKNode } from "@zvisualization/zvs";
import styled from "styled-components";
import { checkPropertyValues } from "../../../cypress/component/Utils/testUtils";

const baseNode: ELKNode = {
    id: "",
    height: 0,
    width: 0,
    x: 0,
    y: 0,
    nodeLabel: "",
    nodeTypeLabel: "",
    typeId: ""
};

const major95open: ELKNode = {
    ...baseNode,
    devData: {
        resource: {
            confidence: 95,
            eventGroupSeverity: 5,
            status: "open"
        }
    }
};

const minor25open: ELKNode = {
    ...baseNode,
    devData: {
        resource: {
            confidence: 25,
            eventGroupSeverity: 4,
            status: "open"
        }
    }
};

const critical99closed: ELKNode = {
    ...baseNode,
    devData: {
        resource: {
            confidence: 99,
            eventGroupSeverity: 6,
            status: "closed"
        }
    }
};

const warning77closed: ELKNode = {
    ...baseNode,
    devData: {
        resource: {
            confidence: 77,
            eventGroupSeverity: 3,
            status: "closed"
        }
    }
};

const ButtonAndPropertyContainer = styled.div`
    height: 400px;
`;

const PropertyWrapper = styled.div`
    margin-left: 200px;
    width: 200px;
    overflow: hidden;
`;

const mountReplacementDecorator = (node: ELKNode) => {
    cy.mount(<ReplacementDecoratorForPanel node={node} overrideShowReplacementDecorator={true} />);
};

describe("ReplacementDecoratorForPanel component test", () => {
    const confidenceText = '[data-cy="event-group-property-data__icon-name-confidence__confidence"';
    const popoverButton = ".cds--popover-container";
    const statusTag = '[data-cy="event-group-property-data__status"]';

    it("Should correctly display severity name, confidence score, and status tag", () => {
        mountReplacementDecorator(critical99closed);
        checkPropertyValues("Critical", 99, false);

        mountReplacementDecorator(major95open);
        checkPropertyValues("Major", 95, true);

        mountReplacementDecorator(minor25open);
        checkPropertyValues("Minor", 25, true);

        mountReplacementDecorator(warning77closed);
        checkPropertyValues("Warning", 77, false);
    });
    it("Should have correct spacing for confidence score, popover button, and status tag", () => {
        const checkConfidenceSpacing = () => {
            cy.get(confidenceText).then((item) => {
                const textElement: HTMLElement = item[0];
                cy.wrap(textElement).should("have.property", "offsetLeft", 107);
            });
        };

        mountReplacementDecorator(critical99closed);
        checkConfidenceSpacing();

        mountReplacementDecorator(major95open);
        checkConfidenceSpacing();

        mountReplacementDecorator(minor25open);
        checkConfidenceSpacing();

        mountReplacementDecorator(warning77closed);
        checkConfidenceSpacing();

        // Check that absolute-positioned popover button is aligned correctly
        cy.get(popoverButton)
            .should("have.css", "position", "absolute")
            .should("have.css", "margin-top", "2px")
            .should("have.css", "margin-inline-start", "-2px");

        cy.get(statusTag).should("have.css", "margin", "0px");
    });
    it("Should not clip popover content", () => {
        cy.mount(
            <ButtonAndPropertyContainer>
                <PropertyWrapper>
                    <ReplacementDecoratorForPanel
                        node={warning77closed}
                        overrideShowReplacementDecorator={true}
                    />
                </PropertyWrapper>
            </ButtonAndPropertyContainer>
        );
        // Check that the expanded content is not clipped by its ancestor's overflow-hidden
        cy.get(".cds--toggletip button").should("exist").click();
        cy.get('[data-cy="event-group-property__toggletip__content"]')
            .should("exist")
            .then((e) => {
                const element: HTMLElement = e[0];
                cy.wrap(element.clientWidth).should("equal", element.scrollWidth);
            });
    });
});
