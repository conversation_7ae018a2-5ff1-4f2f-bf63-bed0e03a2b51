/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React from "react";
import { ToggletipLabel, Toggletip, ToggletipButton, ToggletipContent } from "@carbon/react";
import { Information } from "@carbon/react/icons";
import { ELKNode, DecoratorIconTypes, DecoratorToolTipTypes } from "@zvisualization/zvs";
import i18n from "../../i18n";
import { getStatusDecoratorInfo, shouldDecoratorBeShown } from "../../utils/decoratorUtils";
import ConditionalTag from "../../utils/reusable-components/ConditionalTag";
import Styles from "./ReplacementDecoratorForPanel.styles";
import { Resource } from "../../common/types";
import { isGroupNode } from "../../utils/groupingUtils";
import { StatusConfig } from "../../utils/statusUtils";
import { useConfigurationStore } from "../../store/configuration-store";

export const ReplacementDecoratorForPanel = ({
    node,
    overrideShowReplacementDecorator
}: {
    node: ELKNode;
    overrideShowReplacementDecorator?: boolean; // manual override of tooltip visibility, useful for tests
}) => {
    const resource: Resource | undefined = node.devData?.resource;
    const confidence = useConfigurationStore.getState().confidence;
    const status = useConfigurationStore.getState().status;
    if (
        resource &&
        ((!node.devData?.isUnmonitored && !isGroupNode(node)) || overrideShowReplacementDecorator)
    ) {
        let content = null;
        const eventGroupInfo = getStatusDecoratorInfo(resource.eventGroupSeverity);
        if (
            (shouldDecoratorBeShown(node, confidence, status?.value as StatusConfig | undefined) ||
                overrideShowReplacementDecorator) &&
            eventGroupInfo
        ) {
            const iconType = DecoratorIconTypes[eventGroupInfo.type as DecoratorToolTipTypes];
            const Icon = iconType.icon;

            content = (
                <>
                    <Styles.IconNameConfidence data-cy="event-group-property-data__icon-name-confidence">
                        <Styles.IconWrapper>
                            <Icon
                                fill={iconType.color}
                                width={18}
                                height={18}
                                data-cy="event-group-property-data__icon-name-confidence__icon"
                            />
                        </Styles.IconWrapper>
                        <p>
                            <Styles.StatusName data-cy="event-group-property-data__icon-name-confidence__name">
                                {eventGroupInfo.text}
                            </Styles.StatusName>{" "}
                            |{" "}
                            <span data-cy="event-group-property-data__icon-name-confidence__confidence">
                                {resource.confidence}
                            </span>
                        </p>
                    </Styles.IconNameConfidence>
                    <ConditionalTag condition={resource.status === "closed"}>
                        <Styles.StatusTag
                            type="cool-gray"
                            size="sm"
                            data-cy="event-group-property-data__status"
                        >
                            {i18n.t("EVENT_GROUP_CLOSED")}
                        </Styles.StatusTag>
                    </ConditionalTag>
                </>
            );
        } else {
            content = i18n.t("SEVERITY.NO_TRENDING_EVENT_GROUP");
        }

        return (
            <>
                <Styles.ReplacementDecoratorLabel data-cy="event-group-property">
                    <ToggletipLabel>
                        <div data-cy="event-group-property__toggletip-label">
                            {i18n.t("EVENT_GROUP_TOOLTIP")}
                        </div>
                    </ToggletipLabel>
                    <div>
                        {/* Div wrapper is necessary to enable absolute positioning of popover */}
                        <Toggletip align="bottom-right">
                            <ToggletipButton
                                label={i18n.t("EVENT_GROUP_TOOLTIP_LABEL")}
                                data-cy="event-group-property__toggletip__button"
                            >
                                <Information />
                            </ToggletipButton>
                            <ToggletipContent>
                                <div data-cy="event-group-property__toggletip__content">
                                    {i18n.t("EVENT_GROUP_TOOLTIP_CONTENT")}
                                </div>
                            </ToggletipContent>
                        </Toggletip>
                    </div>
                </Styles.ReplacementDecoratorLabel>
                <Styles.ReplacementDecoratorData data-cy="event-group-property-data">
                    {content}
                </Styles.ReplacementDecoratorData>
            </>
        );
    }
    return null;
};
