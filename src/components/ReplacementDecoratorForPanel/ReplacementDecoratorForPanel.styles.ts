/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { spacing } from "@carbon/layout";
import { Tag } from "@carbon/react";
import styled from "styled-components";

const ReplacementDecoratorData = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
`;

const StatusName = styled.span`
    font-weight: bold;
    display: inline-block;
    margin-inline-end: ${spacing[2]};
    min-width: ${spacing[9]}; // design specs say that dividing line should stay in the same place
`;

const ReplacementDecoratorLabel = styled.div`
    display: flex;
    margin-top: ${spacing[4]};
    margin-bottom: ${spacing[2]};
    .cds--popover-container {
        position: absolute;
        margin-top: ${spacing[0]};
        margin-inline-start: -${spacing[0]};
    }
`;

const IconNameConfidence = styled.div`
    align-items: center;
    display: flex;
`;

const StatusTag = styled(Tag)`
    margin: 0;
`;

const IconWrapper = styled.div`
    margin-right: ${spacing[1]};
`;

export default {
    ReplacementDecoratorData,
    ReplacementDecoratorLabel,
    IconNameConfidence,
    StatusName,
    StatusTag,
    IconWrapper
};
