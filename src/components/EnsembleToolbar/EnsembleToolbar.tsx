/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React from "react";
import styled from "styled-components";
import { gray } from "@carbon/colors";

import SeverityLegend from "./ToolbarSeverityLegend/SeverityLegend";
import ToolbarSearch from "./ToolbarSearch/ToolbarSearch";
import ToolbarSetting from "./ToolbarSetting/ToolbarSetting";

const ToolBar = styled.div`
    border-bottom: ${gray[20]} 1px solid;
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 5;
    height: 2rem;
    width: 100%;
`;
const ToolBarRight = styled.div`
    display: flex;
    padding-right: 12px;
`;

export const EnsembleToolbar = () => {
    return (
        <ToolBar>
            <SeverityLegend />
            <ToolBarRight>
                <ToolbarSearch />
                <ToolbarSetting />
            </ToolBarRight>
        </ToolBar>
    );
};
