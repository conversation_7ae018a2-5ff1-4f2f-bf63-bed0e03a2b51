/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import {
    Popover,
    PopoverContent,
    RadioButtonGroup,
    ButtonSet,
    Toggle,
    FormLabel
} from "@carbon/react";
import styled from "styled-components";

const settingContainerSize = 280;

export const SettingTitle = styled.div`
    padding: 16px;
`;

export const SettingButtonSet = styled(ButtonSet)`
    position: absolute;
    bottom: 0;
    & button {
        width: ${settingContainerSize / 2}px !important;
    }
`;

export const SettingPopover = styled(Popover)``;

export const SettingRadioButtonGroup = styled(RadioButtonGroup)`
    &&& {
        padding: 0 16px 16px;
    }
`;

export const SettingPopoverContent = styled(PopoverContent)`
    width: ${settingContainerSize}px;
    height: ${settingContainerSize}px;
    white-space: nowrap; // To put Monitored by Z Anomaly Analytics on one line
`;

export const SettingToggle = styled(Toggle)`
    &&& {
        padding-left: 16px;
    }
`;

export const DSGToggleLabel = styled(FormLabel)`
    padding: 0 0 5px 16px;
`;
