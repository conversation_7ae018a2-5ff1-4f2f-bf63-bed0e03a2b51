/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { useEffect, useState } from "react";
import { SettingsAdjust } from "@carbon/icons-react";
import { Button, RadioButton, Stack } from "@carbon/react";
import { izoaDashboardData } from "../../../ZDSDataProvider";
import { useFilterStore, useGraphStore, useZvsStore } from "@zvisualization/zvs";
import { IconButton } from "@carbon/react";
import {
    SettingButtonSet,
    SettingPopover,
    SettingPopoverContent,
    SettingRadioButtonGroup,
    SettingTitle,
    SettingToggle,
    DSGToggleLabel
} from "./toolbarSettingStyles";
import useMonitorStore from "../../../store/monitor-store";
import { MonitorState } from "../../../Constants";

const ToolbarSetting = () => {
    const setRefreshData = useZvsStore((state) => state.setRefreshData);
    const elkNodes = useGraphStore((state) => state.nodes);
    const graphNodes = useGraphStore((state) => state.graphNodes);
    const graphEdges = useGraphStore((state) => state.graphEdges);
    const applyFiltering = useFilterStore((state) => state.applyFiltering);
    const toggleBucket = useFilterStore((state) => state.toggleBucket);
    const checkAllForFilter = useFilterStore((state) => state.checkAllForFilter);
    const setInMonitoredState = useMonitorStore((state) => state.setInMonitoredState);
    const inMonitoredState = useMonitorStore((state) => state.inMonitoredState);

    const [selectedMonitorState, setSelectedMonitorState] = useState<string>(
        inMonitoredState ? MonitorState.MONITORED : MonitorState.ALL
    );
    const [open, setOpen] = useState(false);

    const [showDataSharingGroups, setShowDataSharingGroups] = useState(true);
    const [temporaryDSGToggleState, setTemporaryDSGToggleState] = useState(showDataSharingGroups);
    // <------------------------- Helper Functions ------------------------->

    const hasToggleChanges = () => {
        return temporaryDSGToggleState !== showDataSharingGroups;
    };
    const hasMonitorStateChanges = () => {
        if (
            (inMonitoredState && selectedMonitorState === MonitorState.ALL) ||
            (!inMonitoredState && selectedMonitorState === MonitorState.MONITORED)
        ) {
            return true;
        } else {
            return false;
        }
    };

    const toggleDSG = (value: boolean) => {
        setShowDataSharingGroups(value);
        izoaDashboardData.showDataSharingGroups = value;
        setRefreshData(true);
    };

    const handleDataSharingGroupToggleChange = (value: boolean) => {
        setTemporaryDSGToggleState(value);
    };

    const handleSelectedMonitorStateChange = (event: React.MouseEvent<HTMLInputElement>) => {
        const newValue = event.currentTarget.value;
        setSelectedMonitorState(newValue);
    };

    const handleCancel = () => {
        setTemporaryDSGToggleState(showDataSharingGroups);
        setSelectedMonitorState(inMonitoredState ? MonitorState.MONITORED : MonitorState.ALL);
        setOpen(false);
    };

    const applySelectedMonitorStateChanges = () => {
        if (selectedMonitorState === MonitorState.ALL) {
            setInMonitoredState(false);
            checkAllForFilter("filter-by-monitor");
        }
        if (selectedMonitorState === MonitorState.MONITORED) {
            setInMonitoredState(true);
            checkAllForFilter("filter-by-monitor");
            toggleBucket("filter-by-monitor", "Unmonitored");
        }
        applyFiltering(elkNodes, graphEdges);
    };

    const handleApply = () => {
        // If there are dsg changes we only want to apply monitor state change in the useEffect
        if (hasToggleChanges()) {
            toggleDSG(temporaryDSGToggleState);
        } else if (hasMonitorStateChanges()) {
            // If there's no dsg change and only monitor state change then the useEffect won't run
            // So we need to run applySelectedMonitorStateChanges here
            applySelectedMonitorStateChanges();
        }

        // Close the popover
        setOpen(false);
    };

    // <------------------------ End of Helper Functions ------------------------->

    // Need DSG graph data to load first before we apply the monitor filter
    // There's an issue where there's a lot of missing edges otherwise
    useEffect(() => {
        if (hasMonitorStateChanges()) {
            applySelectedMonitorStateChanges();
        }
    }, [graphNodes]);

    // Update selected monitor state if inMonitoredState was changed
    // Ex: When onDisableFilterCallback updates it
    useEffect(() => {
        setSelectedMonitorState(inMonitoredState ? MonitorState.MONITORED : MonitorState.ALL);
    }, [inMonitoredState]);

    return (
        <SettingPopover
            isTabTip
            open={open}
            align="bottom-end"
            dropShadow
            onRequestClose={handleCancel}
            data-testid="toolbar-settings"
        >
            <IconButton
                data-testid="toolbar-settings-button"
                aria-label="Settings"
                type="button"
                kind="ghost"
                size="sm"
                label="Settings"
                onClick={() => {
                    setOpen((prevValue) => !prevValue);
                }}
                disabled={graphNodes.length === 0}
            >
                <SettingsAdjust />
            </IconButton>
            <SettingPopoverContent>
                <SettingTitle data-testid="toolbar-settings-popover-title">
                    Topology view setting
                </SettingTitle>
                <Stack gap={2}>
                    <SettingRadioButtonGroup
                        orientation="vertical"
                        legendText="Node type"
                        name="radio-nodes-group"
                        valueSelected={selectedMonitorState}
                    >
                        <RadioButton
                            labelText="Monitored by Z Anomaly Analytics"
                            value="monitored"
                            onClick={handleSelectedMonitorStateChange}
                            data-testid="monitored-radio-button"
                            id="monitored-radio-button"
                        />
                        <RadioButton
                            labelText="All nodes"
                            value="all"
                            onClick={handleSelectedMonitorStateChange}
                            data-testid="all-nodes-radio-button"
                            id="all-nodes-radio-button"
                        />
                    </SettingRadioButtonGroup>

                    <Stack gap={2}>
                        <DSGToggleLabel>Group by data sharing group</DSGToggleLabel>
                        <SettingToggle
                            onToggle={handleDataSharingGroupToggleChange}
                            toggled={temporaryDSGToggleState}
                            id="sharing-group-toggle"
                            data-testid="sharing-group-toggle"
                        />
                    </Stack>
                </Stack>

                <SettingButtonSet>
                    <Button
                        kind="secondary"
                        onClick={handleCancel}
                        data-testid="cancel-toolbar-settings"
                    >
                        Cancel
                    </Button>
                    <Button
                        kind="primary"
                        onClick={handleApply}
                        disabled={!hasToggleChanges() && !hasMonitorStateChanges()}
                        data-testid="apply-toolbar-settings"
                    >
                        Apply
                    </Button>
                </SettingButtonSet>
            </SettingPopoverContent>
        </SettingPopover>
    );
};

export default ToolbarSetting;
