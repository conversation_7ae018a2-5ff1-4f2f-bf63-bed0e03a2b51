/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import {
    ELKNode,
    resetCheckedNodes,
    useGraphStore,
    useZvsStore,
    ZvsStateData
} from "@zvisualization/zvs";
import { GraphState } from "@zvisualization/zvs/lib/store/graph-store";
import { topologyChannel } from "../../../App";
import {
    ViewResourceDetailsMessage,
    MessageType,
    ErrorMessage
} from "../../../common/messageTypes";
import ConditionalTag from "../../../utils/reusable-components/ConditionalTag";
import {
    StyledSelectedResourcesContainer,
    StyledLeftContainer,
    StyledRightContainer,
    ResourceBarButton,
    VerticalLine
} from "./SelectedResourcesBarStyles";
import { DefaultBroadcastMessageId } from "../../../Constants";

const SelectedResourcesBar = () => {
    // To prevent sending multiple error messages if user continues to select more
    const [errorMessageSent, setErrorMessageSent] = useState(false);
    const checkedNodes = useZvsStore((state: ZvsStateData) => state.checkedNodes);
    const setCheckedNodes = useZvsStore((state: ZvsStateData) => state.setCheckedNodes);
    const graphNodes = useGraphStore((state: GraphState) => state.graphNodes);
    const { t } = useTranslation(["consumer"]);

    const viewResourceDetails = () => {
        const selectedResources = checkedNodes.map((node: ELKNode) => {
            return node?.devData?.resource;
        });

        const msg: ViewResourceDetailsMessage = {
            id: DefaultBroadcastMessageId,
            type: MessageType.VIEW_RESOURCE_DETAILS,
            selectedResources: selectedResources
        };
        topologyChannel.postMessage(msg);
    };

    const cancel = () => {
        resetCheckedNodes(checkedNodes, graphNodes, setCheckedNodes);
        setErrorMessageSent(false);
    };

    useEffect(() => {
        if (checkedNodes.length > 8 && !errorMessageSent) {
            const msg: ErrorMessage = {
                id: DefaultBroadcastMessageId,
                type: MessageType.ERROR,
                message: t("ERROR.SELECTED_RESOURCES_EXCEED_MAXIMUM")
            };
            topologyChannel.postMessage(msg);
            setErrorMessageSent(true);
        } else if (checkedNodes.length <= 8 && errorMessageSent) {
            setErrorMessageSent(false);
        }
    }, [checkedNodes]);

    return (
        <ConditionalTag condition={checkedNodes && checkedNodes.length > 0}>
            <StyledSelectedResourcesContainer>
                <StyledLeftContainer data-cy="resources-bar-selected-count">
                    {t("RESOURCES_SELECTED", { count: checkedNodes.length })}
                </StyledLeftContainer>
                <StyledRightContainer>
                    <ResourceBarButton
                        data-cy="resources-bar-view-resource"
                        disabled={checkedNodes.length > 8}
                        size="sm"
                        onClick={viewResourceDetails}
                    >
                        {t("VIEW_RESOURCE_DETAILS")}
                    </ResourceBarButton>
                    <VerticalLine />
                    <ResourceBarButton data-cy="resources-bar-cancel" size="sm" onClick={cancel}>
                        {t("CANCEL")}
                    </ResourceBarButton>
                </StyledRightContainer>
            </StyledSelectedResourcesContainer>
        </ConditionalTag>
    );
};

export default SelectedResourcesBar;
