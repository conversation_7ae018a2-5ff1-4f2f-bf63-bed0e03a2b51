/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { blue, white } from "@carbon/colors";
import { spacing } from "@carbon/layout";
import { Button } from "@carbon/react";
import styled from "styled-components";

export const StyledSelectedResourcesContainer = styled.div`
    background-color: ${blue[60]};
    color: ${white};
    width: 100%;
    display: flex;
    justify-content: space-between;
`;
export const StyledLeftContainer = styled.div`
    padding: ${spacing[2]} ${spacing[4]};
`;
export const StyledRightContainer = styled.div`
    display: flex;
    align-items: center;
`;

export const VerticalLine = styled.div`
    height: 16px;
    border-left: 1px solid #ffffff;
`;

export const ResourceBarButton = styled(Button)`
    padding-right: ${spacing[4]};
    padding-left: ${spacing[4]};
`;
