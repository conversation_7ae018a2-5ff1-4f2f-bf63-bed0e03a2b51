/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React from "react";
import { Tooltip } from "@carbon/react";
import { Information } from "@carbon/icons-react";
import { useTranslation } from "react-i18next";
import { DecoratorIcon, DecoratorIconTypes } from "@zvisualization/zvs";

import styled from "styled-components";

const SeverityLegendContainer = styled.div`
    display: flex;
    align-items: center;
    font-size: 12px;
    padding-left: 16px;
`;

const LegendTooltipButton = styled.button`
    border: none;
    background: none;
    &:hover {
        background-color: none;
    }
    margin-top: 4px;
    padding-left: 5px;
`;

const LegendDescriptionList = styled.dl`
    display: flex;
    align-items: center;
    dt {
        // the term icon
        margin: 0 4px;
    }
`;

const SeverityLegend = () => {
    const { t } = useTranslation(["consumer"]);
    const tooltipText = t("TOOLBAR.SEVERITY_DESC");
    return (
        <SeverityLegendContainer>
            Severity
            <Tooltip description={tooltipText} align="bottom-start">
                <LegendTooltipButton aria-label={tooltipText}>
                    <Information />
                </LegendTooltipButton>
            </Tooltip>
            <LegendDescriptionList>
                <DecoratorIcon
                    icon={DecoratorIconTypes["error-alt"].icon}
                    color={DecoratorIconTypes["error-alt"].color}
                />
                <dt>{t("SEVERITY.CRITICAL")}</dt>

                <DecoratorIcon
                    icon={DecoratorIconTypes["warning-alt-inverted"].icon}
                    color={DecoratorIconTypes["warning-alt-inverted"].color}
                />
                <dt>{t("SEVERITY.MAJOR")}</dt>

                <DecoratorIcon
                    icon={DecoratorIconTypes["warning-alt"].icon}
                    color={DecoratorIconTypes["warning-alt"].color}
                />
                <dt>{t("SEVERITY.MINOR")}</dt>

                <DecoratorIcon
                    icon={DecoratorIconTypes["warning-square"].icon}
                    color={DecoratorIconTypes["warning-square"].color}
                />
                <dt>{t("SEVERITY.WARNING")}</dt>
            </LegendDescriptionList>
        </SeverityLegendContainer>
    );
};

export default SeverityLegend;
