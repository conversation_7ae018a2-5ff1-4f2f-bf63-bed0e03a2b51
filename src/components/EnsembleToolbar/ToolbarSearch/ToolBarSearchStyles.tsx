/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import styled from "styled-components";
import { gray, white } from "@carbon/colors";
import { rem, spacing } from "@carbon/layout";

interface DropdownProps {
    open: boolean;
}

export const SearchContainer = styled.div<DropdownProps>`
    display: flex;
    align-items: center;

    .cds--text-input,
    .cds--text-input--empty {
        padding-left: ${spacing[6]};
        background-color: white !important;
    }
    .cds--tooltip.cds--icon-tooltip {
        ${(props: DropdownProps) => (props.open ? `margin-right: -${spacing[6]};` : null)}
        z-index: 2;
    }
`;

export const DropdownContainer = styled.div<DropdownProps>`
    opacity: ${(props) => (props.open ? 1 : 0)};
    visibility: ${(props) => (props.open ? "visible" : "hidden")};
    width: ${(props) => (props.open ? rem(384) : "0")};
    position: relative;
    .cds--list-box__menu {
        background-color: ${white};
    }
    .cds--list-box__menu-item__option {
        border: none;
        padding-right: 0; // remove default padding as the checkmark is not part of design
    }
    .cds--list-box__menu-icon {
        // hide chevron used to open/collapse as not part of design
        // I used visually hidden instead of display: none in case this served an important accessibility purpose
        // but oddly enough the button has tabindex of -1 so by default it isn't keyboard focusable?
        clip: rect(0 0 0 0);
        clip-path: inset(50%);
        height: 1px;
        overflow: hidden;
        position: absolute;
        white-space: nowrap;
        width: 1px;
    }
    .cds--list-box__selection {
        // move the X button closer to the right since there is an empty spot where chevron used to be
        right: ${spacing[4]}; // 12px is the right value that the chevron has
    }
    .cds--list-box__menu-item__selected-icon {
        // remove the checkmark which is used to indicate the selected item
        display: none;
    }
`;

export const SearchElement = styled.div`
    display: flex;
    justify-content: space-between;
`;
export const ElementName = styled.div`
    padding-right: ${spacing[7]};
`;
export const ElementPath = styled.div`
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
`;

export const BoldedSearchPiece = styled.span`
    font-weight: bold;
    color: ${gray[100]};
`;

export const NonboldedSearchPiece = styled.span``;

export const NoResultsFoundMessage = styled.span`
    color: ${gray[70]};
    background-color: white;
    font-size: 0.875rem;
    block-size: ${spacing[6]};
    padding: ${spacing[4]};
    padding-block-end: ${rem(7)}; // Taken from carbon styling of combobox items.
    padding-block-start: ${rem(7)};
    box-shadow: 0 2px 6px var(--cds-shadow, rgba(0, 0, 0, 0.3));
    position: absolute;
    width: 100%;
`;
