/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { useEffect, useState } from "react";
import { ComboBox } from "@carbon/react";
import { IconButton } from "@carbon/react";
import { Search } from "@carbon/icons-react";
import {
    useFilterStore,
    useGraphStore,
    usePropertiesStore,
    useTypeRegistry,
    usePropertiesLinkFilterStore,
    jumpToNode
} from "@zvisualization/zvs";
import { getProperType } from "../../../utils/typeUtils";
import { ELKNode, PropertiesLinkFilterBucket } from "@zvisualization/zvs/lib/types/ZvsTypes";
import {
    BoldedSearchPiece,
    DropdownContainer,
    ElementName,
    ElementPath,
    NoResultsFoundMessage,
    NonboldedSearchPiece,
    SearchContainer,
    SearchElement
} from "./ToolBarSearchStyles";
import { MessageType, FilterAction, PostFilterMessage } from "../../../common/messageTypes";
import { topologyChannel } from "../../../App";
import { CheckboxFilter } from "@zvisualization/zvs/lib/store/filter-store/filter-store.types";
import { EventGroupSeverity } from "../../../utils/severityUtils";
import {
    EnsembleFilterItem,
    EnsembleSeverityFilterValues
} from "../../../common/ensembleFilterTypes";
import TruncateTag from "../../../utils/reusable-components/TruncateTag";
import { escapeRegex, nodePath } from "../../../utils/searchUtils";
import ConditionalTag from "../../../utils/reusable-components/ConditionalTag";
import useMonitorStore from "../../../store/monitor-store";
import useFilterMessageStore from "../../../store/filter-message-store";
import { useConfigurationStore } from "../../../store/configuration-store";

const ToolbarSearch = () => {
    const nodes = useGraphStore((state) => state.nodes);
    const expandGroupNode = useGraphStore((state) => state.expandGroupNode);
    const setOnDisableFilterCallback = useGraphStore((state) => state.setOnDisableFilterCallback);
    const getType = useTypeRegistry((state) => state.getType);
    const setFocusedNode = usePropertiesStore((state) => state.setFocusedNode);
    const graphNodes = useGraphStore((state) => state.graphNodes);
    const storedFilterMessage = useFilterMessageStore((state) => state.message);
    const setStoredFilterMessage = useFilterMessageStore((state) => state.setMessage);
    const setShowPropertiesLinkFilterModal = usePropertiesLinkFilterStore(
        (state: any) => state.setShowPropertiesLinkFilterModal
    );
    const filter = useFilterStore((state) => state);
    const setFocusedNodeDomElement = usePropertiesStore((state) => state.setFocusedNodeDomElement);
    const setInMonitoredState = useMonitorStore((state) => state.setInMonitoredState);
    const inMonitoredState = useMonitorStore((state) => state.inMonitoredState);
    const confidence = useConfigurationStore((state) => state.confidence);
    const status = useConfigurationStore((state) => state.status);
    const getFilterById = filter.getFilterById;

    const [open, setOpen] = useState(false);
    const [inputValue, setInputValue] = useState("");

    /**
     * Handles the logic when filters are disabled. Applies the selected filters and sends a message via the topology channel.
     *
     * @returns {void}
     */
    const onDisableFilterCallback = (buckets: PropertiesLinkFilterBucket[]): void => {
        const changedFilters = new Set(buckets.map((bucket) => bucket.filterId));
        try {
            const typeFilter = getFilterById("filter-by-type") as CheckboxFilter;
            const severityFilter = getFilterById("filter-by-severity") as CheckboxFilter;
            const monitorFilter = getFilterById("filter-by-monitor") as CheckboxFilter;
            const subsystemFilter = getFilterById("filter-by-subsystem") as CheckboxFilter;

            if (!typeFilter || !severityFilter || !monitorFilter || !subsystemFilter) {
                throw new Error("Failed to retrieve filters.");
            }

            const postMessage: PostFilterMessage = {
                id: "TOPOLOGYML",
                type: MessageType.FILTER_DATA,
                action: FilterAction.Apply,
                data: {
                    confidence: confidence,
                    status: status,
                    subsystem: storedFilterMessage?.data.subsystem ?? [],
                    resourceType: storedFilterMessage?.data.resourceType ?? [],
                    eventGroupSeverity: storedFilterMessage?.data.eventGroupSeverity ?? []
                }
            };

            if (changedFilters.has("filter-by-severity")) {
                const activeSupportedSeverityGroups: EnsembleFilterItem[] = Array.from(
                    severityFilter.activeBuckets
                ).flatMap((severity) => {
                    // FlatMap acts like a map and filters out nulls in the array
                    const ensembleSeverityFilterItem =
                        EnsembleSeverityFilterValues[
                            EventGroupSeverity[severity as keyof typeof EventGroupSeverity]
                        ];
                    return ensembleSeverityFilterItem ? [ensembleSeverityFilterItem] : [];
                });
                postMessage.data.eventGroupSeverity = activeSupportedSeverityGroups;
            }

            if (monitorFilter.isChecked("Unmonitored")) {
                setInMonitoredState(false);
            }

            if (changedFilters.has("filter-by-type")) {
                const activeSupportedFilters: EnsembleFilterItem[] = Array.from(
                    typeFilter.activeBuckets
                )
                    .filter((type: string) => {
                        const lowercaseType = type.toLowerCase();
                        return (
                            getProperType[lowercaseType] &&
                            getProperType[lowercaseType].isSupportedByEnsemble
                        );
                    })
                    .flatMap((type) => {
                        const ensembleTypeFilterItem = getProperType[type].ensembleFilterType;
                        return ensembleTypeFilterItem ? [ensembleTypeFilterItem] : [];
                    });
                postMessage.data.resourceType = activeSupportedFilters;
            }

            if (changedFilters.has("filter-by-subsystem")) {
                const activeSubsystemFilters: EnsembleFilterItem[] = Array.from(
                    subsystemFilter.activeBuckets
                ).map((subsystem) => ({ content: subsystem, selected: true }));
                postMessage.data.subsystem = activeSubsystemFilters;
            }

            topologyChannel.postMessage(postMessage);
            setStoredFilterMessage(postMessage);
        } catch (error: any) {
            console.error("Error occurred:", error.message);
            // Handle the error here, show user-friendly messages, or perform other necessary actions.
        }
    };

    const toggleDropdown = () => {
        setOpen(!open);
    };

    useEffect(() => {
        // Sets onDisableFilterCallback that will be called when jumping to nodes through properties panel
        setOnDisableFilterCallback(onDisableFilterCallback);
    }, []);

    const filteredNodes = nodes.filter(
        (node) =>
            node.metadata.name.toLowerCase().includes(inputValue.toLowerCase()) &&
            (!inMonitoredState || !node.devData.isUnmonitored)
    );

    const nodeToElement = (node: ELKNode) => {
        const name: string | undefined = node.metadata.name;
        if (name && inputValue) {
            const escapedUserInput = escapeRegex(inputValue);
            const splitName = name.split(new RegExp(`(${escapedUserInput})`, "ig"));
            const splitNameWithoutEmptyStrings = splitName.filter((piece) => piece !== "");
            const styledPieces = splitNameWithoutEmptyStrings.map((piece, i) => {
                if (piece.toLocaleLowerCase() === inputValue.toLocaleLowerCase()) {
                    return <BoldedSearchPiece key={i}>{piece}</BoldedSearchPiece>;
                } else {
                    return <NonboldedSearchPiece key={i}>{piece}</NonboldedSearchPiece>;
                }
            });
            return (
                <SearchElement data-testid={`search-option-${name}`}>
                    <ElementName>{styledPieces}</ElementName>
                    <ElementPath>
                        <TruncateTag>{nodePath(node)}</TruncateTag>
                    </ElementPath>
                </SearchElement>
            );
        }
        return (
            <SearchElement>
                <ElementName>{node.metadata.name}</ElementName>
                <ElementPath>
                    <TruncateTag>{nodePath(node)}</TruncateTag>
                </ElementPath>
            </SearchElement>
        );
    };

    return (
        <SearchContainer open={open}>
            <IconButton
                onClick={toggleDropdown}
                kind="ghost"
                size="sm"
                label="Search"
                disabled={nodes.length <= 0}
                data-testid="open-search-button"
            >
                <Search />
            </IconButton>

            <DropdownContainer open={open}>
                <ComboBox
                    size="sm"
                    aria-label="Choose a node"
                    items={filteredNodes}
                    disabled={nodes.length <= 0}
                    onChange={({ selectedItem }) => {
                        const selectedNode: ELKNode = selectedItem as ELKNode;
                        if (selectedNode) {
                            jumpToNode(
                                selectedNode.id,
                                nodes,
                                graphNodes,
                                filter,
                                setShowPropertiesLinkFilterModal,
                                setFocusedNodeDomElement,
                                setFocusedNode,
                                expandGroupNode,
                                getType,
                                onDisableFilterCallback
                            );
                        }
                    }}
                    id="carbon-combobox"
                    data-testid="search-combobox"
                    itemToString={(node: any) => (node ? node.metadata.name : "")}
                    onInputChange={(event) => setInputValue(event)}
                    itemToElement={nodeToElement}
                />
                <span id="no-results-found" aria-live="polite">
                    <ConditionalTag condition={filteredNodes.length === 0}>
                        <NoResultsFoundMessage>No results found.</NoResultsFoundMessage>
                    </ConditionalTag>
                </span>
            </DropdownContainer>
        </SearchContainer>
    );
};

export default ToolbarSearch;
