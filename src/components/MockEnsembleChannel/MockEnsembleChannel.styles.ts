/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { MultiSelect } from "@carbon/react";
import styled from "styled-components";

const MockEnsembleControls = styled.div`
    border: 1px solid gray;
    padding: 1rem;
    h2 {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
    }
    form {
        border: 1px solid #eaeaea;
        padding: 1rem;
        margin: 0 0.5rem 0 0;
        display: flex;
        flex-direction: column;
        row-gap: 0.5rem;
        min-width: 230px;
    }
`;

const MockEnsembleForms = styled.div`
    display: flex;
`;

const FilterContainer = styled.div`
    padding: 10px;
`;

const MultiSelectWrapper = styled(MultiSelect)`
    .bx--list-box__field--wrapper {
        display: flex;
        align-items: center;
        height: 100%;
    }
    .bx--tag {
        flex: 1 0 auto;
    }
`;

export default {
    MockEnsembleControls,
    MockEnsembleForms,
    FilterContainer,
    MultiSelectWrapper
};
