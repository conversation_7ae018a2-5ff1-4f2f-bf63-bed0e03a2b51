/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { useEffect, useState } from "react";
import { BroadcastChannel } from "broadcast-channel";
import { OnChangeData, Dropdown, NumberInput, Button, Form } from "@carbon/react";
import {
    BroadcastMessage,
    ErrorMessage,
    FilterAction,
    MessageType,
    PostFilterMessage,
    ResetFilterMessage,
    TimeSelectedMessage,
    ViewResourceDetailsMessage
} from "../../common/messageTypes";
import { formatDateForBroadcastChannel, formatDateForInputElement } from "../../utils/dateUtils";
import Styles from "./MockEnsembleChannel.styles";
import {
    DefaultBroadcastChannelName,
    DefaultConfidenceScoreConfig,
    DefaultStatusConfig
} from "../../Constants";
import {
    getNumberFromEnsembleFilterValue,
    EnsembleFilterItem
} from "../../common/ensembleFilterTypes";
import { StatusConfig, EnsembleStatusValues } from "../../utils/statusUtils";
import { getEnsembleFilterTypeFromEnsembleTypeName } from "../../utils/typeUtils";
import { getSeverityValue } from "../../utils/severityUtils";

const ensembleSenderAndReceiver = new BroadcastChannel(DefaultBroadcastChannelName);
/**
 * Mock broadcast channel representing ensemble.
 * Handles both sending and receiving of messages.
 */
export const MockEnsembleChannel = () => {
    const [chosenTypes, setChosenTypes] = useState<string[]>([]);
    const [chosenSubsystems, setChosenSubsystems] = useState<string[]>([]);
    const [selectedSeverities, setSelectedSeverities] = useState<string[]>([]);

    const [status, setStatus] = useState(DefaultStatusConfig.value as StatusConfig);
    const [confidenceFrom, setConfidenceFrom] = useState(DefaultConfidenceScoreConfig.from);
    const [confidenceTo, setConfidenceTo] = useState(DefaultConfidenceScoreConfig.to);

    const defaultFromDate = new Date();
    const defaultToDate = new Date();
    defaultToDate.setDate(defaultFromDate.getDate() + 6);

    // Message receiving
    const ensembleReceiptHandler = (message: BroadcastMessage) => {
        switch (message.type) {
            case MessageType.VIEW_RESOURCE_DETAILS:
                const selectedResources = (message as ViewResourceDetailsMessage).selectedResources;
                if (selectedResources) {
                    const resourceIds = selectedResources.map((res) => res.resourceId);
                    if (resourceIds) {
                        console.log(
                            "Received openResourceView message in ensembleHandler:",
                            resourceIds
                        );
                    }
                }
                break;
            case MessageType.FILTER_DATA:
                console.log(`Received new filter by type message: ${JSON.stringify(message)}`);
                break;
            case MessageType.ERROR:
                const errorMessage = (message as ErrorMessage).message;
                if (errorMessage) {
                    console.log("Received ErrorMessage in ensembleHandler:", message);
                }
            case undefined:
                break;
        }
    };

    // Setup message receipt handling
    useEffect(() => {
        ensembleSenderAndReceiver.addEventListener("message", ensembleReceiptHandler);
        return () => {
            ensembleSenderAndReceiver.removeEventListener("message", ensembleReceiptHandler);
        };
    }, []);

    // Message sending
    const resetFilter = () => {
        const msg: ResetFilterMessage = { type: MessageType.RESET_FILTER, id: "mock" };
        ensembleSenderAndReceiver.postMessage(msg);
    };

    const handleDate = (formData: FormData) => {
        const fromDate = formData.get("startDate")?.toString();
        const toDate = formData.get("endDate")?.toString();
        if (fromDate && toDate) {
            const fromDateObject = new Date(Date.parse(fromDate));
            const toDateObject = new Date(Date.parse(toDate));
            // Message has format Thu, 01 Jan 1970 00:00:00 UTC
            const fromDateString = formatDateForBroadcastChannel(fromDateObject);
            const toDateString = formatDateForBroadcastChannel(toDateObject);
            const msg: TimeSelectedMessage = {
                type: MessageType.TIME_SELECTED,
                timePickerSelection: {
                    lastUsed: "",
                    fromDate: fromDateString,
                    interval: "24h",
                    toDate: toDateString,
                    source: ""
                },
                id: "mock"
            };
            ensembleSenderAndReceiver.postMessage(msg);
        }
    };

    const updateStatus = (event: OnChangeData<StatusConfig>) => {
        setStatus(event.selectedItem!);
    };

    const updateConfidenceFrom = (
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        state: {
            value: string | number;
            direction: string;
        }
    ) => {
        const value = getNumberFromEnsembleFilterValue(state.value);
        if (value) {
            setConfidenceFrom(value);
        }
    };

    const updateConfidenceTo = (
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        state: {
            value: string | number;
            direction: string;
        }
    ) => {
        const value = getNumberFromEnsembleFilterValue(state.value);
        if (value) {
            setConfidenceTo(value);
        }
    };

    const postFilterData = (
        types: string[],
        subsystems: string[],
        severities: string[],
        action: FilterAction
    ) => {
        const postableSubsystems = subsystems.map((item) => ({
            content: item,
            selected: true
        }));

        const postableTypesData = types.flatMap((type) => {
            const ensembleTypeFilterItem = getEnsembleFilterTypeFromEnsembleTypeName(type);
            return ensembleTypeFilterItem ? [ensembleTypeFilterItem] : [];
        });

        const postableSeverityData: EnsembleFilterItem[] = severities.map((severity: string) => ({
            content: severity,
            selected: true,
            value: getSeverityValue(severity)
        }));

        const msg: PostFilterMessage = {
            id: "mock",
            type: MessageType.FILTER_DATA,
            action: action,
            data: {
                subsystem: postableSubsystems,
                resourceType: postableTypesData,
                eventGroupSeverity: postableSeverityData,
                confidence: { from: confidenceFrom, to: confidenceTo },
                status: EnsembleStatusValues[status]
            }
        };

        ensembleSenderAndReceiver.postMessage(msg);
    };

    const postSavedData = (action: FilterAction) =>
        postFilterData(chosenTypes, chosenSubsystems, selectedSeverities, action);

    return (
        <Styles.FilterContainer>
            <Styles.MockEnsembleControls>
                <h2>Mock ensemble</h2>
                <Styles.MockEnsembleForms>
                    <form name="filterForm" onSubmit={(e) => e.preventDefault()}>
                        <label>Type Filter</label>
                        <input
                            placeholder="type filter"
                            type="text"
                            onChange={(e) => {
                                setChosenTypes(e.target.value ? e.target.value.split(",") : []);
                            }}
                        />
                        <button
                            onClick={(e) => {
                                e.preventDefault();
                                postSavedData(FilterAction.Preview);
                            }}
                        >
                            Preview Filter
                        </button>
                        <button
                            onClick={() => {
                                postSavedData(FilterAction.Cancel);
                            }}
                        >
                            Cancel Preview Filter
                        </button>
                        <Button
                            size="sm"
                            onClick={(e) => {
                                e.preventDefault();
                                postSavedData(FilterAction.Apply);
                            }}
                        >
                            Apply Filters
                        </Button>
                        <Button
                            kind="danger"
                            size="sm"
                            data-testid="reset-filters-btn"
                            onClick={resetFilter}
                        >
                            Reset All Filters
                        </Button>
                    </form>
                    <form
                        name="datePicker"
                        aria-labelledby="date-form-label"
                        onSubmit={(e) => e.preventDefault()}
                    >
                        <label id="date-form-label">Date range picker</label>
                        <label>
                            Start date
                            <input
                                type="datetime-local"
                                id="start-date"
                                name="startDate"
                                defaultValue={formatDateForInputElement(defaultFromDate)}
                            />
                        </label>
                        <label>
                            End date
                            <input
                                type="datetime-local"
                                id="end-date"
                                name="endDate"
                                defaultValue={formatDateForInputElement(defaultToDate)}
                            />
                        </label>
                        <input
                            type="submit"
                            value="Submit"
                            onClick={(e) => {
                                e.preventDefault();
                                const form = (e.target as HTMLInputElement).form;
                                const formData = new FormData(form ?? undefined);
                                handleDate(formData);
                            }}
                        />
                    </form>
                    <form name="subsystem" aria-labelledby="subsystem-label">
                        <label id="subsystem-label">Sysplex/system filter</label>
                        <Styles.MultiSelectWrapper
                            id="subsystem-filter"
                            titleText="Subsystem"
                            onChange={(data: {
                                selectedItems: {
                                    id: string;
                                    label: string;
                                }[];
                            }) => {
                                const choices = data.selectedItems.map((item) => item.label);
                                setChosenSubsystems(choices);
                                postFilterData(
                                    chosenTypes,
                                    choices,
                                    selectedSeverities,
                                    FilterAction.Preview
                                );
                            }}
                            label="Subsystem"
                            items={[
                                { id: "item0", label: "LPAR400J:SYSG" },
                                { id: "item1", label: "LPAR400J:SYSF" },
                                { id: "item2", label: "LPAR400J:J90" }
                            ]}
                        />
                    </form>
                    <form
                        name="statusConfidence"
                        aria-labelledby="statusConfidence-form-label"
                        onSubmit={(e) => e.preventDefault()}
                    >
                        <Dropdown
                            id="status-dropdown"
                            aria-label="status-dropdown"
                            titleText={"Status"}
                            label={<>status</>}
                            selectedItem={status}
                            initialSelectedItem="all"
                            items={Object.values(StatusConfig)}
                            onChange={updateStatus}
                            itemToString={(item) =>
                                item === StatusConfig.All ? "Open and Closed" : "Open"
                            }
                        />
                        <label>Confidence</label>
                        <NumberInput
                            id="confidence-from"
                            min={0}
                            max={100}
                            value={confidenceFrom}
                            label="From"
                            onChange={updateConfidenceFrom}
                        />
                        <NumberInput
                            id="confidence-to"
                            min={0}
                            max={100}
                            value={confidenceTo}
                            label="To"
                            onChange={updateConfidenceTo}
                        />
                    </form>

                    <Form aria-label="Severities-label">
                        <label id="severities-label">Severities</label>
                        <Styles.MultiSelectWrapper
                            id="severity-filter"
                            titleText="Severity"
                            onChange={(data: {
                                selectedItems: {
                                    id: string;
                                    label: string;
                                }[];
                            }) => {
                                const chosenSeverities = data.selectedItems.map(
                                    (item) => item.label
                                );
                                setSelectedSeverities(chosenSeverities);
                                postFilterData(
                                    chosenTypes,
                                    chosenSubsystems,
                                    chosenSeverities,
                                    FilterAction.Preview
                                );
                            }}
                            label="Severities"
                            items={[
                                { id: "major", label: "Major" },
                                { id: "minor", label: "Minor" },
                                { id: "critical", label: "Critical" },
                                { id: "warning", label: "Warning" },
                                { id: "no-trending", label: "No Trending Event Group" }
                            ]}
                        />
                    </Form>
                </Styles.MockEnsembleForms>
            </Styles.MockEnsembleControls>
        </Styles.FilterContainer>
    );
};
