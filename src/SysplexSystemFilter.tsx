/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { useEffect } from "react";
import { ELKNode, translationArguments, useFilterStore } from "@zvisualization/zvs";
import { NodeCheckboxFilterOptions } from "@zvisualization/zvs/lib/store/filter-store/filter-store.types";

export const nodeToBucketIdsBySystem = (node: ELKNode): Set<string> => {
    if (node.devData) {
        if (node.devData.systems && node.devData.sysplexes) {
            const systems: string[] = [];
            for (const sysplexLabel of node.devData.sysplexes) {
                const systemsOfSysplex = node.devData.systems.map(
                    (systemLabel: string) => sysplexLabel + ":" + systemLabel
                );
                systemsOfSysplex.forEach((system: string) => systems.push(system));
            }
            return new Set(systems);
        } else if (node.devData.sysplexes && !node.devData.systems) {
            const sysplexes = node.devData.sysplexes.map(
                (sysplexLabel: string) => sysplexLabel + ":" + node.nodeLabel
            );
            return new Set(sysplexes);
        } else if (node.devData.descendantSystems) {
            const systems = node.devData.descendantSystems.map((systemLabel: string) => {
                return node.nodeLabel + ":" + systemLabel;
            });
            return new Set(systems);
        }
    }
    return new Set([]);
};

// Usage in filterArgs
const filterArgs: NodeCheckboxFilterOptions = {
    sortBucketsByOrderSpecified: false,
    filterTitle: translationArguments(""),
    filterDescription: translationArguments(""),
    filterGroupLabel: translationArguments(""),
    nodeToBucketIds: nodeToBucketIdsBySystem,
    bucketLabels: {},
    displayAllBucketsOption: false,
    appearanceCriteria: () => true
};

export const SysplexSystemFilter = () => {
    const register = useFilterStore((state) => state.registerNodeCheckboxFilter);
    useEffect(() => {
        register("filter-by-subsystem", filterArgs);
    }, []);
    return null;
};
