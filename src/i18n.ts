import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import en from "./Locales/en.json";

i18n.use(LanguageDetector)
    .use(initReactI18next)
    .init({
        resources: {
            en: en
        },
        fallbackLng: "en",
        debug: false,

        // have a common namespace used around the full app
        ns: ["consumer", "zvs"],
        defaultNS: "consumer",
        fallbackNS: "zvs",

        keySeparator: ".", // we use content as keys

        interpolation: {
            escapeValue: false
        }
    });

export default i18n;
