/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { ELKNode } from "@zvisualization/zvs";
import { getProperType } from "./typeUtils";

export const isGroupNode = (node: ELKNode) => {
    return node.typeId === "meganode" || isSharingGroupNode(node);
};

export const isSharingGroupNode = (node: ELKNode) => {
    return (
        node.typeId === getProperType.db2datasharinggroup.zvs ||
        node.typeId === getProperType.imssysplexgroup.zvs
    );
};

export const isSharingGroupType = (type: string) => {
    return (
        type.toLowerCase() === getProperType.db2datasharinggroup.zvs ||
        type.toLowerCase() === getProperType.imssysplexgroup.zvs
    );
};
