/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

const ConditionalTag = (props: { condition: boolean | undefined; children: any }) => {
    if (props.condition) {
        return props.children;
    } else {
        return null;
    }
};

export default ConditionalTag;
