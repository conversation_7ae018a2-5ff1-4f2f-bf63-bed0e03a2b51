/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { FunctionComponent, useEffect, useRef, useState } from "react";

interface Props {
    children: any;
    forceTitleOn?: boolean;
}

const TruncateTag: FunctionComponent<Props> = (props: Props) => {
    const ref = useRef<HTMLDivElement>(null);
    const [titleProps, setTitleProps] = useState({});

    useEffect(() => {
        // If text is truncated then add a title
        setTitleProps(
            ref.current != null &&
                (ref.current.offsetWidth < ref.current.scrollWidth || props.forceTitleOn === true)
                ? { title: props.children }
                : {}
        );
    }, [ref.current, ref.current?.offsetWidth, ref.current?.scrollWidth, props.forceTitleOn]);

    return (
        <div
            style={{
                display: "inline-block",
                width: "100%",
                overflow: "hidden",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                lineHeight: "normal"
            }}
            {...props}
            {...titleProps}
            ref={ref}
        >
            {props.children}
        </div>
    );
};

export default TruncateTag;
