/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { ELKNode } from "@zvisualization/zvs";

export enum EventGroupSeverity {
    Critical = 6,
    Major = 5,
    Minor = 4,
    Warning = 3,
    NoTrendingEventGroup = 1,
    None = 0
}

export const getSeverityValue = (severity: string): number => {
    return EventGroupSeverity[severity as keyof typeof EventGroupSeverity];
};

export const validSeveritiesArray: string[] = Object.keys(EventGroupSeverity);

/**
 * Compares ELKNodes based on their event group severity and status/confidence filters.
 *
 * @param {ELKNode} a - The first ELKNode to be compared.
 * @param {ELKNode} b - The second ELKNode to be compared.
 * @return {number} - A negative number if a < b, a positive number if a > b, or 0 if a = b.
 */
export const compareSeverity = (a: ELKNode, b: ELKNode) => {
    const aEventGroupSeverity = !a.devData.isDecoratorHiddenDueToStatusAndConfidence
        ? a.devData?.resource?.eventGroupSeverity ?? 0
        : 1;

    const bEventGroupSeverity = !b.devData.isDecoratorHiddenDueToStatusAndConfidence
        ? b.devData?.resource?.eventGroupSeverity ?? 0
        : 1;

    return bEventGroupSeverity - aEventGroupSeverity;
};

/**
 * Gets the severity number based on the provided severity string.
 *
 * @param {string} severityString - The severity string to be converted to a number.
 * @return {number} - The severity number corresponding to the input string.
 */
export const getEventGroupSeverityFromString = (severityString: string): number => {
    const severity: EventGroupSeverity =
        EventGroupSeverity[severityString as keyof typeof EventGroupSeverity];
    return severity;
};

/**
 * Checks if the provided severity string is supported.
 *
 * @param {string} severityString - The severity string to be checked.
 * @return {boolean} - True if the severity string is supported, otherwise false.
 */
export const isSupportedSeverity = (severityString: string): boolean => {
    const severity: EventGroupSeverity =
        EventGroupSeverity[severityString as keyof typeof EventGroupSeverity];
    if (severity !== undefined) {
        return true;
    }
    return false;
};

/**
 * Determines what bucket id should be returned for a node. Used for severity filters
 *
 * @param {ELKNode} node - The node used to determine bucket id
 * @return {Set<string>} - A set containing the bucket id
 */
export const nodeToBucketIdsForSeverity = (node: ELKNode): Set<string> => {
    if (
        (node.metadata?.decorator &&
            node.devData?.resource?.eventGroupSeverity &&
            !node.devData.isUnmonitored) ||
        (!node.devData.isUnmonitored && !node.metadata.decorator)
    ) {
        // If eventGroupSeverity is 2 then treat it as 1 for no trending event group
        // If the decorator is hidden due to the status and confidence filters then treat it as no trending event group
        const eventGroupSeverity =
            node.devData?.resource?.eventGroupSeverity === 2 ||
            node.devData.isDecoratorHiddenDueToStatusAndConfidence
                ? 1
                : node.devData?.resource?.eventGroupSeverity;
        if (eventGroupSeverity) {
            return new Set([EventGroupSeverity[eventGroupSeverity]]);
        }
    }
    return new Set(["None"]);
};
