/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { EnsembleFilterItem } from "../common/ensembleFilterTypes";

export enum StatusConfig {
    Open = "open",
    All = "all"
}

export enum ResourceStatus {
    Open = "open",
    Closed = "closed"
}

export type EnsembleStatusFilterType = { [key in StatusConfig]: EnsembleFilterItem };

export const EnsembleStatusValues: EnsembleStatusFilterType = {
    [StatusConfig.Open]: { content: "Open", selected: true, value: "open" },
    [StatusConfig.All]: { content: "Open and closed", selected: true, value: "all" }
};
