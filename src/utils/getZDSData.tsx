/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React from "react";
import { ELKNode } from "@zvisualization/zvs";
import { izoaDashboardData } from "../ZDSDataProvider";
import { EnsembleData, Resource } from "../common/types";
import { getCredentialsHeader } from "../services/auth.service";
import { processConfidenceStatusToAddOrRemoveDecorator } from "./decoratorUtils";
import { isValidType } from "./typeUtils";
import { filterNodesAndEdges } from "./graphDataUtils";
import { useConfigurationStore } from "../store/configuration-store";
import { ReplacementDecoratorForPanel } from "../components/ReplacementDecoratorForPanel/ReplacementDecoratorForPanel";

/**
 * Check if the resource matches the node by checking that its name, the system and sysplexes its connected to
 * matches the resource. We try to match systems/sysplexes but sometimes its undefined like for ZOS or Sysplex
 * so we just match by name for those
 *
 * @param {Resource} resourceGroup The resource group data
 * @param {ELKNode} node Node data
 * @returns {boolean} True if it matches, false otherwise
 * */
const doesResourceMatchNode = (resourceGroup: Resource, node: ELKNode): boolean => {
    if (node.typeId === "cicregion") {
        console.log("CICS detected");
        console.log(node);
    }
    return resourceGroup.resourceName === node.nodeLabel;
    const nameMatches = resourceGroup.resourceName === node.nodeLabel;
    const systemMatches =
        !node.devData.systems || node.devData.systems?.includes(resourceGroup.nodeName);
    const sysplexMatches =
        !node.devData.sysplexes || node.devData.sysplexes?.includes(resourceGroup.clusterName);

    return nameMatches && systemMatches && sysplexMatches;
};

const processResourceForNode = (node: ELKNode, resourceGroupSummary: Resource[]) => {
    //by default, set every node's isUnmonitored flag to true
    node.devData.isUnmonitored = true;

    const isTypeValid = isValidType(node.typeId);

    if (resourceGroupSummary && isTypeValid) {
        const resourceGroup = resourceGroupSummary.find((resourceGroup: Resource) => {
            return doesResourceMatchNode(resourceGroup, node);
        });

        if (resourceGroup) {
            node.devData.isUnmonitored = false;
            node.devData.resource = resourceGroup;
            processConfidenceStatusToAddOrRemoveDecorator(
                node,
                useConfigurationStore.getState().confidence,
                useConfigurationStore.getState().status
            );
            // Add custom status decorator replacement to be displayed at the top of the properties panel
            // (For non-group nodes.)
            node.devData.replacementDecoratorElement = <ReplacementDecoratorForPanel node={node} />;
            node.devData.showReplacementDecoratorInProperties = true;
        }
    }
};

// Not sure why but this needs to be in this file or it will cause strange i18n issue where it displays TYPES.ZOS instead of Zos
// I tried to move it to graphDataUtils, ZDSDataProvider and making a new utils resourceUtils.ts but it caused the issue
export const processResourceGroupSummaryFromResponse = (response: any) => {
    const resourceGroupSummary: Resource[] = izoaDashboardData.resourceGroupSummary;

    // ZVS is looking for specific fields in the node, for ex: typeId, nodeLabel
    // This is more transformation of the data for ZVS to render
    // ZVS supports types that are lowercased, so transform the data to use lower case letters
    for (const key of Object.keys(response.nodes)) {
        const node = response.nodes[key];
        processResourceForNode(node, resourceGroupSummary);
    }
};

export const processResourceGroupSummaryForNodes = (nodes: ELKNode[]) => {
    const resourceGroupSummary: Resource[] = izoaDashboardData.resourceGroupSummary;

    // ZVS is looking for specific fields in the node, for ex: typeId, nodeLabel
    // This is more transformation of the data for ZVS to render
    // ZVS supports types that are lowercased, so transform the data to use lower case letters
    for (const node of nodes) {
        processResourceForNode(node, resourceGroupSummary);
    }
};

export const getCachedZDSData = async (
    request: string,
    key: string,
    izoaDashboardData: EnsembleData,
    cacheTimeInMinutes: number
) => {
    const cachedTime = cacheTimeInMinutes * 60 * 1000; // Convert minutes to milliseconds
    const cachedData = localStorage.getItem(key);
    if (cachedData) {
        const { timestamp, data } = JSON.parse(cachedData);

        if (Date.now() - timestamp < cachedTime) {
            return data;
        }
    }

    const data = await getZDSData(request, izoaDashboardData);
    if (data) {
        filterNodesAndEdges(data, true); //keep DSGs in stored data
        const cachedDataObj = { timestamp: Date.now(), data };
        localStorage.setItem(key, JSON.stringify(cachedDataObj));
    }
    return data;
};

/**
 * Makes a graph request to the ZDS data either with a static token or with keycloak token
 *
 * @param request URL request
 * @param izoaDashboardData data to check if a keycloak token is being used
 * */
export async function getZDSData(request: string, izoaDashboardData: EnsembleData) {
    const headers = getCredentialsHeader(izoaDashboardData);

    const data = await fetch(request, {
        credentials: "include",
        headers: headers
    })
        .then((response) => {
            if (response.ok) {
                return response.json();
            }
            throw new Error(response.statusText);
        })
        .catch((error) => {
            console.warn(error);
            return null;
        });
    return data;
}
