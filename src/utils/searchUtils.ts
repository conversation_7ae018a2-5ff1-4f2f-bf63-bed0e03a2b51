/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { ELKNode } from "@zvisualization/zvs";
import { t } from "./translationUtils";

/**
 * Escapes all regex special characters to provide accurate regex input.
 * source: MDN
 */
export const escapeRegex = (input: string): string => {
    return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

/**
 * Return the path of the given node displayed in the search field.
 *
 * @param {ELKNode} node - The node object to be formatted.
 * @return {string} - The relational path to the node, e.g. SysplexName > SystemName
 */
export const nodePath = (node: ELKNode): string | null => {
    if (node.devData) {
        const sysplexes: string[] | undefined = node.devData.sysplexes;
        const systems: string[] | undefined = node.devData.systems;
        let sysplex: string | null = null;
        let system: string | null = null;
        if (sysplexes && sysplexes.length > 0) {
            sysplex =
                sysplexes.length > 1
                    ? (t("NUM_SYSPLEX", { count: sysplexes.length }) as string)
                    : sysplexes[0];
        }
        if (systems && systems.length > 0) {
            system =
                systems.length > 1
                    ? (t("NUM_SYSTEMS", { count: systems.length }) as string)
                    : systems[0];
        }
        if (sysplex && system) {
            return sysplex + " > " + system;
        } else if (sysplex) {
            return sysplex;
        }
    }
    return null;
};
