/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import i18n from "../i18n";

/**
 * TFunction which can be used outside component
 */
export const t = (key: string, options: any) => i18n.t(key, { ...options, ns: "consumer" });
