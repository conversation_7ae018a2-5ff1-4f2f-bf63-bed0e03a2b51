/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { Edge, Node, NodesAndEdges } from "@zvisualization/zvs/lib/types/ZvsTypes";
import i18n from "../i18n";
import { processResourceGroupSummaryFromResponse } from "./getZDSData";
import { getProperType, zdsToZvsTypesMap } from "./typeUtils";
import { isSharingGroupType } from "./groupingUtils";

/**
 * This method goes through the nodes and add the proper nodeTypeLabel that will be displayed
 * on the properties panel. Then it will add the event severity decorators and get the
 * relationships between nodes which is will use to display Connections In/Out on the
 * properties panel.
 *
 * @param graphData graph data containing nodes and edges
 * */
export const processZDSGraphData = (graphData: any) => {
    for (const key of Object.keys(graphData.nodes)) {
        const node = graphData.nodes[key];
        if (node.nodeType) {
            const translationKey =
                getProperType[(node.nodeType as string).toLowerCase()].translationKey;

            node.nodeTypeLabel = i18n.t(translationKey, {
                count: 1
            });
        } else {
            node.nodeTypeLabel = node.nodeType;
        }

        node.metadata.type = node.nodeTypeLabel;
        node.nodeType = zdsToZvsTypesMap.get(node.nodeType);
        node.typeId = node.nodeType;
        node.nodeLabel = node.metadata.name;
        //checking if devData exist, if not, initialize it
        if (!node.devData) {
            node.devData = {};
        }
    }
    processSubsystems(graphData as NodesAndEdges);
    processResourceGroupSummaryFromResponse(graphData);
    getRelationships(graphData);

    return graphData;
};
/**
 * This method filters out nodes that are not supported. If the type of the node is not in
 * the zdsToZvsTypesMap it is not supported. As well any edge that references a node that
 * is unsupported must be removed
 *
 * @param graphData graph data containing nodes and edges
 * */
export const filterNodesAndEdges = (graphData: any, showDataSharingGroups: boolean) => {
    const unsupportedNodeIds: { [nodeId: string]: boolean } = {};
    const supportedNodes: any = {};
    const nodes = graphData.nodes;
    const edges = graphData.edges as any[];
    const nodeKeys = Object.keys(nodes);
    let zdsTypes = Array.from(zdsToZvsTypesMap.values());

    if (!showDataSharingGroups) {
        zdsTypes = zdsTypes.filter((type) => {
            return !isSharingGroupType(type);
        });
    }

    // Find all the supported and unsupported nodes
    nodeKeys.forEach((nodeId) => {
        const node = nodes[nodeId];
        const typeId = zdsToZvsTypesMap.get(node.nodeType);

        if (typeId && zdsTypes.includes(typeId)) {
            supportedNodes[nodeId] = node;
        } else {
            unsupportedNodeIds[nodeId] = true;
        }
    });
    // Find all the edges where both the source and target nodes are supported.
    const supportedEdges = edges.filter((edge: Edge) => {
        if (isSharingGroupType(nodes[edge.source].nodeType) && edge.relation === "federates") {
            supportedNodes[edge.target].metadata["Data sharing group"] =
                nodes[edge.source].metadata.name;
        }

        return !(unsupportedNodeIds[edge.source] || unsupportedNodeIds[edge.target]);
    });

    graphData.nodes = supportedNodes;
    graphData.edges = supportedEdges;
};

export const processSubsystems = (data: NodesAndEdges): void => {
    if (!data || !data.nodes || !data.edges) {
        return;
    }

    const nodes = { ...data.nodes };
    const edges = [...data.edges];

    const sourceNodeIdToEdge: { [sourceNodeId: string]: Edge[] } = {};
    const targetNodeIdToEdge: { [targetNodeId: string]: Edge[] } = {};

    for (const edge of edges) {
        // Skip edges with "federates" relations because they are edges from group node members to the enclosing group node
        // Since "contains" edges from group nodes to their members already exist, "federates" edges would be duplicates that cause a cyclical graph
        if (edge.relation !== "federates") {
            const sourceId = edge.source;
            if (!sourceNodeIdToEdge[sourceId]) {
                sourceNodeIdToEdge[sourceId] = [edge];
            } else {
                sourceNodeIdToEdge[sourceId].push(edge);
            }
            const targetId = edge.target;
            if (!targetNodeIdToEdge[targetId]) {
                targetNodeIdToEdge[targetId] = [edge];
            } else {
                targetNodeIdToEdge[targetId].push(edge);
            }
        }
    }

    const getParents = (nodeId: string): string[] => {
        const parentIds: string[] = targetNodeIdToEdge[nodeId]?.map((edge) => edge.source) ?? [];
        return parentIds;
    };

    const getChildren = (nodeId: string): string[] => {
        const childIds: string[] = sourceNodeIdToEdge[nodeId]?.map((edge) => edge.target) ?? [];
        return childIds;
    };

    const getAncestors = (currNodeId: string, visited: string[]) => {
        if (visited.includes(currNodeId)) {
            return [];
        }
        visited.push(currNodeId);
        const parents = getParents(currNodeId);
        let ancestors = new Set<string>([]);
        for (const parent of parents) {
            const ancestorsOfParent = getAncestors(parent, visited);
            ancestors = new Set([...Array.from(ancestors), ...ancestorsOfParent, parent]);
        }
        return [...Array.from(ancestors)];
    };

    const getDescendants = (currNodeId: string, visited: string[]): string[] => {
        if (visited.includes(currNodeId)) {
            return [];
        }
        visited.push(currNodeId);
        const children = getChildren(currNodeId);
        let descendants = new Set<string>([]);
        for (const child of children) {
            const descendantsOfChild = getDescendants(child, visited);
            descendants = new Set([...Array.from(descendants), ...descendantsOfChild, child]);
        }
        return [...Array.from(descendants)];
    };

    const isSystem = (node: Node) => node.typeId === getProperType.zos.zvs;
    const isSysplex = (node: Node) =>
        node.typeId === getProperType.lpar.zvs || node.typeId === getProperType.sysplex.zvs;

    for (const nodeId in nodes) {
        const node: Node = nodes[nodeId];
        if (isSystem(node) || isSysplex(node)) {
            // // Everything to the left
            const ancestors = getAncestors(nodeId, []);
            // // Everything to the right
            const descendants = getDescendants(nodeId, []);
            for (const ancestorId of ancestors) {
                const ancestor = nodes[ancestorId];
                if (ancestor) {
                    if (isSystem(node)) {
                        if (!ancestor.devData.descendantSystems) {
                            ancestor.devData.descendantSystems = [];
                        }
                        ancestor.devData.descendantSystems?.push(node.nodeLabel);
                    }
                    if (isSysplex(node)) {
                        if (!ancestor.devData.descendantSysplexes) {
                            ancestor.devData.descendantSysplexes = [];
                        }
                        ancestor.devData.descendantSysplexes?.push(node.nodeLabel);
                    }
                }
            }
            for (const descendantId of descendants) {
                const descendant = nodes[descendantId];
                if (descendant) {
                    if (isSystem(node)) {
                        if (!descendant.devData.systems) {
                            descendant.devData.systems = [];
                        }
                        descendant.devData.systems?.push(node.nodeLabel);
                    }
                    if (isSysplex(node)) {
                        if (!descendant.devData.sysplexes) {
                            descendant.devData.sysplexes = [];
                        }
                        descendant.devData.sysplexes?.push(node.nodeLabel);
                    }
                }
            }
        }
    }

    data.nodes = nodes;
    data.edges = edges;
};

/**
 * Traverses graph data to get relationships for each node
 *
 * @param data graph data containing nodes and edges
 * */
export const getRelationships = (data: NodesAndEdges) => {
    if (!data || !data.nodes || !data.edges) {
        return data;
    }

    const nodes = data.nodes;
    const edges = data.edges;

    edges.forEach((edge: Edge) => {
        (nodes[edge.target].metadata.incomingLabels =
            nodes[edge.target].metadata.incomingLabels ?? []).push(
            nodes[edge.source].metadata.name ?? nodes[edge.source].metadata.jobName
        );
        (nodes[edge.target].metadata.incomingLabelsIds =
            nodes[edge.target].metadata.incomingLabelsIds ?? []).push(edge.source);
        (nodes[edge.target].metadata.incomingTypes =
            nodes[edge.target].metadata.incomingTypes ?? []).push(edge.relation);
        (nodes[edge.source].metadata.outgoingLabels =
            nodes[edge.source].metadata.outgoingLabels ?? []).push(
            nodes[edge.target].metadata.name ?? nodes[edge.target].metadata.jobName
        );
        (nodes[edge.source].metadata.outgoingLabelsIds =
            nodes[edge.source].metadata.outgoingLabelsIds ?? []).push(edge.target);
        (nodes[edge.source].metadata.outgoingTypes =
            nodes[edge.source].metadata.outgoingTypes ?? []).push(edge.relation);
    });
};
