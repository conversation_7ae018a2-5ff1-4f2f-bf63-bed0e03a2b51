/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { EnsembleFilterItem } from "../common/ensembleFilterTypes";

interface ApplicationTypes {
    ensemble: string;
    ensembleFilterType: EnsembleFilterItem | null;
    isSupportedByEnsemble: boolean;
    translationKey: string;
    zds: string;
    zvs: string;
}

export const getProperType: Record<string, ApplicationTypes> = {
    imssubsystem: {
        ensemble: "IMS",
        ensembleFilterType: { content: "IMS", selected: true, value: "IMS" },
        isSupportedByEnsemble: true,
        translationKey: "TYPES.IMS_SUBSYSTEM",
        zds: "IMSSubsystem",
        zvs: "imssubsystem"
    },
    mqsubsystem: {
        ensemble: "MQ",
        ensembleFilterType: { content: "MQ", selected: true, value: "MQ" },
        isSupportedByEnsemble: true,
        translationKey: "TYPES.MQ_SUBSYSTEM",
        zds: "MQSubsystem",
        zvs: "mqsubsystem"
    },
    cicsregion: {
        ensemble: "CICS",
        ensembleFilterType: { content: "CICS", selected: true, value: "CICS" },
        isSupportedByEnsemble: true,
        translationKey: "TYPES.CICS_REGION",
        zds: "CICSRegion",
        zvs: "cicsregion"
    },
    db2subsystem: {
        ensemble: "DB2",
        ensembleFilterType: { content: "Db2", selected: true, value: "DB2" },
        isSupportedByEnsemble: true,
        translationKey: "TYPES.DB2_SUBSYSTEM",
        zds: "DB2Subsystem",
        zvs: "db2subsystem"
    },
    zos: {
        ensemble: "ZOS",
        ensembleFilterType: { content: "z/OS", selected: true, value: "ZOS" },
        isSupportedByEnsemble: true,
        translationKey: "TYPES.ZOS",
        zds: "ZOS",
        zvs: "zos"
    },
    lpar: {
        ensemble: "LPAR",
        ensembleFilterType: null,
        isSupportedByEnsemble: false,
        translationKey: "TYPES.LPAR",
        zds: "LPAR",
        zvs: "lpar"
    },
    db2datasharinggroup: {
        ensemble: "DSG",
        ensembleFilterType: null,
        isSupportedByEnsemble: true,
        translationKey: "TYPES.DB2_DATA_SHARING_GROUP",
        zds: "DB2DataSharingGroup",
        zvs: "db2datasharinggroup"
    },
    imssysplexgroup: {
        ensemble: "DSG",
        ensembleFilterType: null,
        isSupportedByEnsemble: true,
        translationKey: "TYPES.IMS_SYSPLEX_GROUP",
        zds: "IMSSysplexGroup",
        zvs: "imssysplexgroup"
    },
    sysplex: {
        ensemble: "SYSPLEX",
        ensembleFilterType: null,
        isSupportedByEnsemble: true,
        translationKey: "TYPES.SYSPLEX",
        zds: "Sysplex",
        zvs: "sysplex"
    }
};

/**
 * The function `getEnsembleFilterTypeFromEnsembleTypeName` takes an ensemble type name as input and returns the
 * corresponding ZVS type name.
 * @param {string} ensembleTypeName - The `ensembleTypeName` parameter is a string that represents the
 * name of an ensemble type.
 * @returns a string value.
 */
export const getEnsembleFilterTypeFromEnsembleTypeName = (
    ensembleTypeName: string
): EnsembleFilterItem | null => {
    const entry = Object.values(getProperType).find(
        (type) => type.ensemble.toLowerCase() === ensembleTypeName.toLowerCase()
    );
    return entry?.ensembleFilterType ?? null;
};
/**
 * The function `getZdsTypeFromEnsembleTypeName` takes an ensembleTypeName as input and returns the
 * corresponding zdsTypeName.
 * @param {string} ensembleTypeName - The `ensembleTypeName` parameter is a string that represents the
 * name of an ensemble type.
 * @returns a string value.
 */
export const getZdsTypeFromEnsembleTypeName = (ensembleTypeName: string): string => {
    const entry = Object.values(getProperType).find((type) => type.ensemble === ensembleTypeName);
    return entry?.zds ?? "";
};
/**
 * The function `getZvsTypeFromEnsembleTypeName` takes an ensemble type name as input and returns the
 * corresponding ZVS type name.
 * @param {string} ensembleTypeName - The `ensembleTypeName` parameter is a string that represents the
 * name of an ensemble type.
 * @returns a string value.
 */
export const getZvsTypeFromEnsembleTypeName = (ensembleTypeName: string): string => {
    const entry = Object.values(getProperType).find(
        (type) => type.ensemble.toLowerCase() === ensembleTypeName.toLowerCase()
    );
    return entry?.zvs ?? "";
};

function generateZdsTypeNameMap(data: Record<string, ApplicationTypes>): Map<string, string> {
    const zdsTypeNameMap = new Map<string, string>();

    Object.keys(data).forEach((key) => {
        const { zds, isSupportedByEnsemble } = data[key];
        if (isSupportedByEnsemble) {
            zdsTypeNameMap.set(zds, key);
        }
    });

    return zdsTypeNameMap;
}

export const zdsToZvsTypesMap = generateZdsTypeNameMap(getProperType);

/**
 * The function checks if a given typeId matches any of the types in the zdsToZvsTypesMap.
 * @param {string} typeId - The `typeId` parameter is a string that represents the ID of a type.
 * @returns a boolean value. It returns true if the `typeId` parameter is included in the `types`
 * array, and false otherwise.
 */
export const isValidType = (typeId: string) => {
    const types = Array.from(zdsToZvsTypesMap.values());
    if (types.includes(typeId)) {
        return true;
    } else {
        return false;
    }
};
