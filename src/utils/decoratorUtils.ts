/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { DecoratorToolTipTypes, ELKNode } from "@zvisualization/zvs";
import i18n from "../i18n";
import { EventGroupSeverity } from "./severityUtils";
import { Confidence, EnsembleFilterItem } from "../common/ensembleFilterTypes";
import { isGroupNode } from "./groupingUtils";
import { Resource } from "../common/types";
import { StatusConfig, ResourceStatus } from "./statusUtils";

/**
 * Populates the correct status decorator text and icon for different event group severities
 *
 * @param eventGroupSeverity event group severity of an artifact
 * */
export const getStatusDecoratorInfo = (eventGroupSeverity: number) => {
    if (
        eventGroupSeverity >= EventGroupSeverity.Warning &&
        eventGroupSeverity <= EventGroupSeverity.Critical
    ) {
        let iconType = "";
        const statusDecorator = {
            type: iconType,
            text: ""
        };

        if (eventGroupSeverity === EventGroupSeverity.Critical) {
            iconType = DecoratorToolTipTypes.Error_Alt;
            statusDecorator.text = i18n.t("SEVERITY.CRITICAL");
        } else if (eventGroupSeverity === EventGroupSeverity.Major) {
            iconType = DecoratorToolTipTypes.Warning_Alt_Inverted;
            statusDecorator.text = i18n.t("SEVERITY.MAJOR");
        } else if (eventGroupSeverity === EventGroupSeverity.Minor) {
            iconType = DecoratorToolTipTypes.Warning_Alt;
            statusDecorator.text = i18n.t("SEVERITY.MINOR");
        } else if (eventGroupSeverity === EventGroupSeverity.Warning) {
            iconType = DecoratorToolTipTypes.Warning_Square;
            statusDecorator.text = i18n.t("SEVERITY.WARNING");
        }
        statusDecorator.type = iconType;

        return statusDecorator;
    }
    return null;
};

/**
 * Determines if decorator should be shown on a node depending on status and confidence filter
 *
 * @param {ELKNode} node - The node used to check its status, open or closed
 * @param {Confidence | undefined} ensembleFilterConfidence - Ensemble's filter confidence range
 * @param { StatusConfig | undefined} statusConfig - Ensemble's filter status (open or all)
 * @return {boolean} - A boolean for whether a decorator should be shown based on status and confidence
 */
export const shouldDecoratorBeShown = (
    node: ELKNode,
    ensembleFilterConfidence: Confidence | undefined,
    statusConfig: StatusConfig | undefined
) => {
    if (!ensembleFilterConfidence) {
        return true;
    }
    const resource: Resource = node.devData?.resource;
    if (!statusConfig || statusConfig === StatusConfig.Open) {
        // If filter is set to open then if the resource's status is open and within
        // confidence range then it should show decorator
        return (
            resource?.status === ResourceStatus.Open &&
            ensembleFilterConfidence?.from <= resource.confidence &&
            ensembleFilterConfidence?.to >= resource.confidence
        );
    } else if (statusConfig === StatusConfig.All) {
        return (
            ensembleFilterConfidence?.from <= resource?.confidence &&
            ensembleFilterConfidence?.to >= resource?.confidence
        );
    }
};

/**
 * Adds decorator to a node depending on the current confidence and status filter
 *
 * @param {ELKNode} node - The node a decorator will be added to
 * @param {Confidence | undefined} confidence - Ensemble's filter confidence range
 * @param { StatusConfig | undefined} status - Ensemble's filter status (open or all)
 */
export const processConfidenceStatusToAddOrRemoveDecorator = (
    node: ELKNode,
    confidence: Confidence,
    status: EnsembleFilterItem
) => {
    if (!node.devData?.isUnmonitored && !isGroupNode(node)) {
        if (shouldDecoratorBeShown(node, confidence, status?.value as StatusConfig | undefined)) {
            const statusDecorator = getStatusDecoratorInfo(
                node.devData?.resource.eventGroupSeverity
            );
            node.metadata.decorator = statusDecorator;
            node.devData.isDecoratorHiddenDueToStatusAndConfidence = false;
        } else {
            node.metadata.decorator = null;
            node.devData.isDecoratorHiddenDueToStatusAndConfidence = true;
        }
    }
};
