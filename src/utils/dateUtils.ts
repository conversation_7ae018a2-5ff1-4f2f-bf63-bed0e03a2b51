/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

/**
 * Returns date in format 2023-03-31T06:33
 * (ISOString with time zone, seconds, and milliseconds removed)
 */
export const formatDateForInputElement = (d: Date) => {
    return d.toISOString().substring(0, 16);
};

/**
 * Returns date in UTC format
 * Instead of timezone being listed as 'GMT', broadcast channel requires 'UTC' at the end
 * so a replacement is performed.
 */
export const formatDateForBroadcastChannel = (d: Date) => {
    return d.toUTCString().replace("GMT", "UTC");
};
