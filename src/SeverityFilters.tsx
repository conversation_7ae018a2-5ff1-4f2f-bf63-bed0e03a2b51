/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { useEffect } from "react";
import { useFilterStore, ELKNode, translationArguments } from "@zvisualization/zvs";
import { isSharingGroupNode } from "./utils/groupingUtils";
import { nodeToBucketIdsForSeverity } from "./utils/severityUtils";

const SeverityFilters = () => {
    const register = useFilterStore((state) => state.registerNodeCheckboxFilter);
    useEffect(() => {
        register("filter-by-severity", {
            sortBucketsByOrderSpecified: false,
            preventBucketDeleteOnFillBuckets: true,
            filterTitle: translationArguments("FILTER.SEVERITY_LEVEL"),
            filterDescription: translationArguments("FILTER.SEVERITY_LEVEL_DESCRIPTION"),
            filterGroupLabel: translationArguments("FILTER.SEVERITY_LEVEL_GROUP_LABEL"),
            nodeToBucketIds: (node: ELKNode) => {
                //sysplex nodes should always be displayed
                // Don't apply to sharing group for now
                if (node.typeId === "sysplex" || isSharingGroupNode(node)) {
                    return new Set(["AlwaysOn"]);
                }
                return nodeToBucketIdsForSeverity(node);
            },
            bucketLabels: {
                Warning: {
                    text: translationArguments("SEVERITY.WARNING"),
                    displayCount: false
                },
                Minor: {
                    text: translationArguments("SEVERITY.MINOR"),
                    displayCount: false
                },
                Major: {
                    text: translationArguments("SEVERITY.MAJOR"),
                    displayCount: false
                },
                Critical: {
                    text: translationArguments("SEVERITY.CRITICAL"),
                    displayCount: false
                },
                NoTrendingEventGroup: {
                    text: translationArguments("SEVERITY.NO_TRENDING_EVENT_GROUP"),
                    displayCount: false
                },
                None: {
                    text: translationArguments("FILTER.NO_EVENT_GROUP"),
                    displayCount: false,
                    hidden: true
                },
                AlwaysOn: {
                    text: translationArguments("SEVERITY.ALWAYS_ON"),
                    displayCount: false,
                    hidden: true
                }
            },
            displayAllBucketsOption: false,
            appearanceCriteria: (graphNodes: ELKNode[]) => {
                return graphNodes.some(
                    (node) => node && (node.metadata?.decorator || !node.devData?.isUnmonitored)
                );
            }
        });
    }, []);
    return null;
};

export default SeverityFilters;
