/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { create } from "zustand";
import { DefaultConfidenceScoreConfig, DefaultStatusConfig } from "../Constants";
import { Confidence, EnsembleFilterItem } from "../common/ensembleFilterTypes";

interface ConfigurationState {
    confidence: { from: number; to: number };
    status: EnsembleFilterItem;
    setConfidence: (confidence: Confidence) => void;
    setStatus: (status: EnsembleFilterItem) => void;
    reset: () => void;
}

const initialState = { confidence: DefaultConfidenceScoreConfig, status: DefaultStatusConfig };

export const useConfigurationStore = create<ConfigurationState>()((set) => ({
    ...initialState,
    setConfidence: (newConfidence: Confidence) => set({ confidence: newConfidence }),
    setStatus: (newStatus: EnsembleFilterItem) => set({ status: newStatus }),

    reset: () => {
        set(initialState);
    }
}));
