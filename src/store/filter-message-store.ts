/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { create } from "zustand";
import { PostFilterMessage } from "../common/messageTypes";

export interface FilterMessageState {
    message: PostFilterMessage | null;
    setMessage: (msg: PostFilterMessage) => void;
}

const useFilterMessageStore = create<FilterMessageState>((set) => ({
    message: null,
    setMessage: (msg: PostFilterMessage) => {
        set(() => ({
            message: msg
        }));
    }
}));

export default useFilterMessageStore;
