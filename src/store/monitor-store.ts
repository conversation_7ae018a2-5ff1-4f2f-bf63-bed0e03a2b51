/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { create } from "zustand";

type monitorStateType = {
    inMonitoredState: boolean;
    setInMonitoredState: (state: boolean) => void;
    reset: () => void;
};

const initialState = { inMonitoredState: true };

const useMonitorStore = create<monitorStateType>((set) => ({
    ...initialState,
    setInMonitoredState: (newMonitorState: boolean) => set({ inMonitoredState: newMonitorState }),

    reset: () => {
        set(initialState);
    }
}));

export default useMonitorStore;
