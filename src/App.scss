/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

@use "@carbon/react";
$css--body: false;
$css--reset: false;
$css--font-face: false;
@import "carbon-components/scss/globals/grid/_grid.scss";
@import "carbon-components/scss/components/button/button";
@import "carbon-components/scss/components/tooltip/tooltip";
@import "carbon-components/scss/components/tag/tag";
@import "carbon-components/scss/components/link/link";
@import "carbon-components/scss/components/pagination/pagination";
@import "carbon-components/scss/components/loading/loading";
@import "carbon-components/scss/components/checkbox/checkbox";
@import "carbon-components/scss/components/code-snippet/code-snippet";
@import "carbon-components/scss/components/dropdown/dropdown";
@import "carbon-components/scss/components/data-table/data-table";
@import "carbon-components/scss/components/modal/modal";
@import "carbon-components/scss/components/search/search";
@import "carbon-components/scss/components/inline-loading/inline-loading";
@import "carbon-components/scss/components/overflow-menu/overflow-menu";
@import "carbon-components/scss/components/notification/inline-notification";
@import "carbon-components/scss/components/text-input/text-input";
@import "carbon-components/scss/components/checkbox/checkbox";
@import "carbon-components/scss/components/tabs/tabs";
@import "carbon-components/scss/components/number-input/number-input";
@import "carbon-components/scss/components/breadcrumb/breadcrumb";
@import "carbon-components/scss/components/toggle/toggle";
@import "carbon-components/scss/components/accordion/accordion";

// Remove the top bar at both full screen and screen below 827px
[data-testid="graphmenubar-container"] nav,
[data-testid="graphmenubar-container"] div,
[aria-label="Project title"] {
    display: none !important;
}

#graph-diagram-container {
    height: 100vh;
}

// /* Hide scrollbar for Chrome, Safari and Opera */
// body::-webkit-scrollbar,
// #graph-diagram::-webkit-scrollbar {
//     display: none;
// }

// /* Hide scrollbar for IE and Edge */
// body,
// #graph-diagram {
//     -ms-overflow-style: none;
//     scrollbar-width: none; /* Firefox */
// }
