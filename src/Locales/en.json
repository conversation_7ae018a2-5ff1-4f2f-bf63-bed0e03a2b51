{"consumer": {"GRAPH_VIEW": "Topology view", "VIEW_PROPERTIES": "View resource properties", "FILTER": {"SEVERITY_LEVEL": "By event group severity", "SEVERITY_LEVEL_DESCRIPTION": "Filter displayed status decorators", "SEVERITY_LEVEL_GROUP_LABEL": "Severity", "NO_EVENT_GROUP": "No event group", "BY_SOURCE_TITLE": "By data source", "BY_SOURCE_DESCRIPTION": "Filter resources by data", "BY_SOURCE_GROUP_LABEL": "Data provider", "SOURCES": {"ALL_SOURCES": "All sources ({{allCount}})", "IZAA": "IZAA-specific"}, "MONITOR": "Show all or only monitored nodes", "MONITOR_FILTER": " By monitor status", "MONITOR_GROUP_LABEL": "Monitor status", "ALL": "All"}, "SEVERITY": {"CRITICAL": "Critical", "MAJOR": "Major", "MINOR": "Minor", "WARNING": "Warning", "NO_TRENDING_EVENT_GROUP": "No trending event group", "ALWAYS_ON": "Always Displayed"}, "MONITOR": {"MONITORED": "Monitored nodes", "UNMONITORED": "Unmonitored nodes"}, "SELECT_NODE": "Display in resource view", "UNSELECT_NODE": "Hide from resource view", "SELECT_ALL_NODES": "Display in resource view", "UNSELECT_ALL_NODES": "Hide from resource view", "TOOLBAR": {"REGION_LABEL": "Severity legend and graph view options", "SEVERITY_DESC": "Display the severity of the most relevant event group within the time range for each resource."}, "RESOURCES_SELECTED_one": "{{count}} resource selected", "RESOURCES_SELECTED_other": "{{count}} resources selected", "VIEW_RESOURCE_DETAILS": "View resource details", "CANCEL": "Cancel", "ERROR": {"SELECTED_RESOURCES_EXCEED_MAXIMUM": "Selected resources exceed the maximum. Deselect resources to ensure selected resources are not more than 8."}, "NUM_SYSPLEX": "Sysplex ({{count}})", "NUM_SYSTEMS": "zOS systems ({{count}})", "EVENT_GROUP_CLOSED": "Closed", "EVENT_GROUP_TOOLTIP": "Event group overview", "EVENT_GROUP_TOOLTIP_LABEL": "Show more information about event groups", "EVENT_GROUP_TOOLTIP_CONTENT": "Display the attributes of the most relevant event group within the time range.", "EXCLUDED_BY_CONFIGURATION_SETTING": "Excluded by configuration setting"}}