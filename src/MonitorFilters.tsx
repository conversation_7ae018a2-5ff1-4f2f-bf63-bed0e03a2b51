/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { useEffect } from "react";
import { useFilterStore, ELKNode, translationArguments } from "@zvisualization/zvs";
import { getProperType } from "./utils/typeUtils";

const MonitorFilters = () => {
    const register = useFilterStore((state) => state.registerNodeCheckboxFilter);
    useEffect(() => {
        register("filter-by-monitor", {
            sortBucketsByOrderSpecified: false,
            allBucketsLabel: translationArguments("FILTER.ALL"),
            filterTitle: translationArguments("FILTER.MONITOR_FILTER"),
            filterDescription: translationArguments("FILTER.MONITOR"),
            filterGroupLabel: translationArguments("FILTER.MONITOR_GROUP_LABEL"),
            nodeToBucketIds: (node: ELKNode) => {
                if (node.typeId === getProperType.sysplex.zvs) {
                    return new Set(["AlwaysOn"]);
                } else if (node.devData?.isUnmonitored === true) {
                    return new Set(["Unmonitored"]);
                } else {
                    return new Set(["Monitored"]);
                }
            },
            bucketLabels: {
                AlwaysOn: {
                    text: translationArguments("SEVERITY.ALWAYS_ON"),
                    displayCount: false
                },
                Monitored: {
                    text: translationArguments("MONITOR.MONITORED"),
                    displayCount: false
                },
                Unmonitored: {
                    text: translationArguments("MONITOR.UNMONITORED"),
                    displayCount: false
                }
            },
            displayAllBucketsOption: true,
            appearanceCriteria: () => {
                return true;
            }
        });
    }, []);
    return null;
};

export default MonitorFilters;
