/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { Confidence, EnsembleFilterItem } from "./ensembleFilterTypes";
import { Resource } from "./types";

export enum MessageType {
    STOP_LOADING = "insight:stopLoadingIndicator",
    FILTER_DATA = "insight:filterData",
    RESET_FILTER = "insight:resetFilter",
    VIEW_RESOURCE_DETAILS = "insight:viewResource",
    ERROR = "insight:error",
    TIME_SELECTED = "insight:timeSelected"
}

export interface BroadcastMessage {
    type: string;
    id: string;
}
export interface StopLoadingMessage extends BroadcastMessage {
    type: MessageType.STOP_LOADING;
    appId?: string;
}

export enum FilterAction {
    Preview = "preview",
    Apply = "apply",
    Cancel = "cancel"
}
export interface PostFilterMessage extends BroadcastMessage {
    type: MessageType.FILTER_DATA;
    action: FilterAction;
    data: {
        subsystem: EnsembleFilterItem[];
        resourceType: EnsembleFilterItem[];
        eventGroupSeverity: EnsembleFilterItem[];
        confidence: Confidence | null;
        status: EnsembleFilterItem | null;
    };
}
export interface ResetFilterMessage extends BroadcastMessage {
    type: MessageType.RESET_FILTER;
}
export interface ViewResourceDetailsMessage extends BroadcastMessage {
    type: MessageType.VIEW_RESOURCE_DETAILS;
    selectedResources: Resource[];
}
export interface ErrorMessage extends BroadcastMessage {
    type: MessageType.ERROR;
    message: string;
}
export interface TimeSelectedMessage extends BroadcastMessage {
    type: MessageType.TIME_SELECTED;
    timePickerSelection: {
        lastUsed: string;
        fromDate: string;
        interval: string;
        toDate: string;
        source: string;
    };
}
