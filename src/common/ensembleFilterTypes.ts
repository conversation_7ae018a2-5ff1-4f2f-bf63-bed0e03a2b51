/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { EventGroupSeverity } from "../utils/severityUtils";

export interface Confidence {
    from: number;
    to: number;
}
export interface EnsembleFilterItem {
    content: string;
    selected: boolean;
    value?: string | number;
}

type EnsembleSeverityFilterType = {
    [key in EventGroupSeverity]: EnsembleFilterItem | null;
};

export const EnsembleSeverityFilterValues: EnsembleSeverityFilterType = {
    [EventGroupSeverity.Critical]: {
        content: "Critical",
        selected: true,
        value: EventGroupSeverity.Critical
    },
    [EventGroupSeverity.Major]: {
        content: "Major",
        selected: true,
        value: EventGroupSeverity.Major
    },
    [EventGroupSeverity.Minor]: {
        content: "Minor",
        selected: true,
        value: EventGroupSeverity.Minor
    },
    [EventGroupSeverity.Warning]: {
        content: "Warning",
        selected: true,
        value: EventGroupSeverity.Warning
    },
    [EventGroupSeverity.NoTrendingEventGroup]: {
        content: "No trending event group",
        selected: true,
        value: EventGroupSeverity.NoTrendingEventGroup
    },
    [EventGroupSeverity.None]: null
};

/**
 * The function gets number value from Ensemble Filter value which could be string | number | undefined
 * @param {string | number | undefined} value - A value that will be converted to a number
 * @returns a number or undefined
 */
export const getNumberFromEnsembleFilterValue = (value: string | number | undefined) => {
    return typeof value === "string" ? (!isNaN(Number(value)) ? Number(value) : undefined) : value;
};
