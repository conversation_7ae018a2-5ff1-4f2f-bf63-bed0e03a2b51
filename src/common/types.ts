/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

export interface Resource {
    /**
     * clusterName: The connected Sysplex Ex: LPAR400J
     */
    clusterName: string;
    /**
     * nodeName: The connected ZOS Ex: SYSG
     */
    nodeName: string;
    resourceType: string;
    resourceName: string;
    resourceId: string;
    eventGroupSeverity: number;
    lastEventTimestamp: string;
    status: string;
    confidence: number;
}

export interface ApiResource extends Omit<Resource, "eventGroupSeverity" | "confidence"> {
    eventGroupSeverity: string;
    confidence: string;
}

export interface UrlParams {
    urlParams: Array<UrlParam>;
}

export interface UrlParam {
    key: string;
    value: unknown;
}

export interface AppProps {
    izoaData: UrlParams;
}
export interface EnsembleData {
    getKeycloakToken: any;
    resourceGroupSummary: Resource[];
    showDataSharingGroups: boolean;
}
