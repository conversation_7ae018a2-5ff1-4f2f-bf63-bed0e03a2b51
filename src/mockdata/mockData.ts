/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { DefaultConfidenceScoreConfig, DefaultStatusConfig } from "../Constants";

export const generateMockData = () => {
    return {
        urlParams: [
            // USE FOR DC1X & CICSTSTS DEVELOPMENT -- UPDATED CICS KPIs & Modified CICS Summary Intervals
            { key: "id", value: "37412614-d215-49b1-98f1-1bafe8c9f377" },
            { key: "token", value: "123" },
            {
                key: "eventData",
                value: {
                    filter: {
                        subsystem: [],
                        resourceType: [],
                        eventGroupSeverity: [],
                        confidence: DefaultConfidenceScoreConfig,
                        status: DefaultStatusConfig
                    }
                }
            }
        ]
    };
};
