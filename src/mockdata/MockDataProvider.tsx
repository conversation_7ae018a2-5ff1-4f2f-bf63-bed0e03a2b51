/* eslint-disable @typescript-eslint/no-unused-vars */
/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { LPAR400J } from "./LPAR400J";
import { filterNodesAndEdges, processZDSGraphData } from "../utils/graphDataUtils";
import { izoaDashboardData } from "../ZDSDataProvider";

export const mockDataProvider = {
    getArtifactNames: async () => {
        return [];
    },

    getArtifacts: async () => {
        const data = structuredClone(LPAR400J);
        filterNodesAndEdges(data, izoaDashboardData.showDataSharingGroups);
        return processZDSGraphData(data);
    },

    getAdditionalMetadata: async () => {
        return { metadata: {} };
    },
    getManifest: async () => {
        return {
            manifest: {}
        };
    },
    getSearchObjectList: async () => {
        return [];
    }
};
