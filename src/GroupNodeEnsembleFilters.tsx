/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import {
    ELKNode,
    NodeCategorical_GroupOptions,
    translationArguments as tArgs,
    useGroupFilterManagerStore
} from "@zvisualization/zvs";
import { useEffect } from "react";
import { nodeToBucketIdsForSeverity } from "./utils/severityUtils";

const severityFilterOptions: NodeCategorical_GroupOptions = {
    nodeToBucketIds: nodeToBucketIdsForSeverity,
    bucketLabels: {
        Warning: {
            text: tArgs("SEVERITY.WARNING"),
            displayCount: false,
            order: 3
        },
        Minor: {
            text: tArgs("SEVERITY.MINOR"),
            displayCount: false,
            order: 2
        },
        Major: {
            text: tArgs("SEVERITY.MAJOR"),
            displayCount: false,
            order: 1
        },
        Critical: {
            text: tArgs("SEVERITY.CRITICAL"),
            displayCount: false,
            order: 0
        },
        NoTrendingEventGroup: {
            text: tArgs("SEVERITY.NO_TRENDING_EVENT_GROUP"),
            displayCount: false,
            order: 4
        },
        None: {
            text: tArgs("FILTER.NO_EVENT_GROUP"),
            displayCount: false,
            hidden: true
        }
    },
    displayAllBucketsOption: false,
    sortBucketsByOrderSpecified: true,
    schemaTitle: tArgs("FILTER.SEVERITY_LEVEL"),
    schemaDescription: tArgs("FILTER.SEVERITY_LEVEL_DESCRIPTION"),
    closeTagLabel: tArgs("FILTER.SEVERITY_LEVEL_GROUP_LABEL"),
    appearanceCriteria: function (groupNode: ELKNode, graphNodes: ELKNode[]): boolean {
        let allBucketIds: Set<string> = new Set([]);
        for (const memberNode of graphNodes) {
            allBucketIds = new Set([
                ...Array.from(allBucketIds),
                ...Array.from(nodeToBucketIdsForSeverity(memberNode))
            ]);
        }
        return (
            graphNodes.some((node) => {
                return node && (node.metadata?.decorator || !node.devData?.isUnmonitored);
            }) && allBucketIds.size > 1
        );
    }
};

export const GroupNodeEnsembleFilters = () => {
    const addCheckboxFilter = useGroupFilterManagerStore((state) => state.addCheckboxFilter);
    useEffect(() => {
        addCheckboxFilter("group-filter-by-severity", severityFilterOptions);
    }, []);

    return null;
};
