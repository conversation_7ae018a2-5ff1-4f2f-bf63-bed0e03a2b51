/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import { DefaultIzoaDashboardData, ZDS_CACHE_TIME } from "./Constants";
import { getCachedZDSData } from "./utils/getZDSData";
import { EnsembleData } from "./common/types";
import { filterNodesAndEdges, processZDSGraphData } from "./utils/graphDataUtils";

export let izoaDashboardData: EnsembleData = structuredClone(DefaultIzoaDashboardData);

export const resetIzoaDashboardData = () => {
    izoaDashboardData = structuredClone(DefaultIzoaDashboardData);
};

export const dataProvider = {
    getArtifactNames: async () => {
        return [];
    },

    getArtifacts: async (project: string, artifactId: string, relationshipDepth: number) => {
        const colonIndex = artifactId.indexOf(":");
        const nameFilter = artifactId.substring(colonIndex + 1, artifactId.length);

        const getGraphRequest = `/zrdds/api/v1/simpleGraph?depth=${relationshipDepth}&direction=both&id=${nameFilter}`;

        const cacheKey = `${nameFilter}-graphData`; // caching based on the sysplex name

        const response = await getCachedZDSData(
            getGraphRequest,
            cacheKey,
            izoaDashboardData,
            ZDS_CACHE_TIME
        );

        if (response) {
            filterNodesAndEdges(response, izoaDashboardData.showDataSharingGroups);
            return processZDSGraphData(response);
        }
        return null;
    },

    getAdditionalMetadata: async () => {
        return { metadata: {} };
    },
    getManifest: async () => {
        return {
            manifest: {}
        };
    },
    getSearchObjectList: async () => {
        return [];
    }
};
