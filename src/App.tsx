/********************************************************************************
 * Licensed Materials - Property of IBM
 * 5698-ANA (C) Copyright IBM Corp. 2023, 2024
 * All Rights Reserved
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 ********************************************************************************/

import React, { FunctionComponent, useCallback, useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { dataProvider, izoaDashboardData } from "./ZDSDataProvider";
import {
    ZVisualization,
    useZvsStore,
    zvsDataProviderRegistry,
    useTypeRegistry,
    View,
    ZvsStateData,
    ArtifactRenderType,
    ELKNode,
    ActionType,
    useLoadingDataStore,
    usePreferenceStore,
    FeatureConfigOptions,
    defaultFeatureConfig,
    useGraphStore,
    useFilterStore,
    FeatureConfig,
    useGroupFilterManagerStore
} from "@zvisualization/zvs";
import "./App.scss";
import { HeatMap_03 } from "@carbon/icons-react";
import "./i18n";
import SeverityFilters from "./SeverityFilters";
import { BroadcastChannel } from "broadcast-channel";
import { getProperType, getZvsTypeFromEnsembleTypeName } from "./utils/typeUtils";
import { getResourceGroupSummary } from "./services/resource.service";
import { isGroupNode, isSharingGroupNode } from "./utils/groupingUtils";
import { getStatusDecoratorInfo } from "./utils/decoratorUtils";
import { EnsembleToolbar } from "./components/EnsembleToolbar/EnsembleToolbar";
import { AppProps, UrlParams } from "./common/types";
import { GroupNodeEnsembleFilters } from "./GroupNodeEnsembleFilters";
import {
    BroadcastMessage,
    FilterAction,
    MessageType,
    PostFilterMessage,
    StopLoadingMessage,
    TimeSelectedMessage,
    ViewResourceDetailsMessage
} from "./common/messageTypes";
import { CheckboxFilter } from "@zvisualization/zvs/lib/store/filter-store/filter-store.types";
import SelectedResourcesBar from "./components/EnsembleToolbar/SelectedResourcesBar/SelectedResourcesBar";
import ConditionalTag from "./utils/reusable-components/ConditionalTag";
import {
    EventGroupSeverity,
    compareSeverity,
    nodeToBucketIdsForSeverity,
    validSeveritiesArray
} from "./utils/severityUtils";
import { StyledToolbarContainer, TopologyContainer } from "./AppStyles";
import {
    CustomElementId,
    DefaultBroadcastChannelName,
    DefaultBroadcastMessageId,
    resetMessage
} from "./Constants";
import MonitorFilters from "./MonitorFilters";
import useMonitorStore from "./store/monitor-store";
import {
    EnsembleFilterItem,
    EnsembleSeverityFilterValues,
    getNumberFromEnsembleFilterValue
} from "./common/ensembleFilterTypes";
import { ELKEdge } from "@zvisualization/zvs/lib/types/ZvsTypes";
import { nodeToBucketIdsBySystem, SysplexSystemFilter } from "./SysplexSystemFilter";
import { Observable } from "rxjs";
import { MockEnsembleChannel } from "./components/MockEnsembleChannel/MockEnsembleChannel";
import { mockDataProvider } from "./mockdata/MockDataProvider";
import { generateMockData } from "./mockdata/mockData";
import { useConfigurationStore } from "./store/configuration-store";
import useFilterMessageStore from "./store/filter-message-store";
import { ReplacementDecoratorForPanel } from "./components/ReplacementDecoratorForPanel/ReplacementDecoratorForPanel";
import { StatusConfig } from "./utils/statusUtils";
import { processResourceGroupSummaryForNodes } from "./utils/getZDSData";

export const topologyChannel = new BroadcastChannel(DefaultBroadcastChannelName);

const App: FunctionComponent<AppProps> = ({ izoaData }) => {
    // Create map from izoaData urlParams
    let izoaMap = new Map();
    try {
        izoaMap = new Map(
            izoaData.urlParams.map((obj: { key: string; value: any }): [string, any] => [
                obj.key,
                obj.value
            ])
        );
    } catch (error) {
        console.error("No Data Provided. urlParams does not exist in izoaData");
        console.log(error);
    }
    // Format example from IZAA: 2023-08-30T14:11:04.641Z
    const initialFromDateString = izoaMap.get("fromDate");
    const initialToDateString = izoaMap.get("toDate");
    const initialFromDate: Date = initialFromDateString
        ? new Date(Date.parse(initialFromDateString))
        : new Date();
    const initialToDate: Date = initialFromDateString
        ? new Date(Date.parse(initialToDateString))
        : new Date();

    // <------------------ App States  ------------------>
    const [fromDate, setFromDate] = useState(initialFromDate);
    const [toDate, setToDate] = useState(initialToDate);
    const [resetHeuristicKey, setResetHeuristicKey] = useState(0);
    // Used to determine if the graph data should be processed to add group node decorators based on status/confidence
    const [shouldProcessGroupNodes, setShouldProcessGroupNodes] = useState(true);
    const [shouldLoadInitialFilters, setShouldLoadInitialFilters] = useState(false);
    const [showSelectedResourcesBar, setShowSelectedResourcesBar] = useState(false);
    // Keeps track of previous filter message so it can be reapplied when toggling data sharing group
    const storedFilterMessage = useFilterMessageStore((state) => state.message);
    const setStoredFilterMessage = useFilterMessageStore((state) => state.setMessage);

    // <------------------ States from Stores ------------------>
    const setView = useZvsStore((state: ZvsStateData) => state.setView);
    const setDefaultState = useZvsStore((state: ZvsStateData) => state.setDefaultState);
    const setSelectedNodeIdAndTypeId = useZvsStore(
        (state: ZvsStateData) => state.setSelectedNodeIdAndTypeId
    );
    const setFeatureValue = useZvsStore((state: ZvsStateData) => state.setFeatureValue);
    const setFeatureConfig = useZvsStore((state: ZvsStateData) => state.setFeatureConfig);
    const setSelectedNodeLabel = useZvsStore((state: ZvsStateData) => state.setSelectedNodeLabel);
    const checkedNodes = useZvsStore((state: ZvsStateData) => state.checkedNodes);
    const setAllSelectedNodeIds = useZvsStore((state) => state.setAllSelectedNodeIds);
    const setConfig = useZvsStore((state) => state.setConfig);

    const getType = useTypeRegistry((state) => state.getType);
    const appendActionsToType = useTypeRegistry((state) => state.appendActionsToType);
    const elkNodes = useGraphStore((state) => state.nodes);
    const graphNodes = useGraphStore((state) => state.graphNodes);
    const graphEdges = useGraphStore((state) => state.graphEdges);
    const setRedrawOnFilter = useGraphStore((state) => state.setRedrawOnFilter);
    const setIsDataLoading = useLoadingDataStore((state) => state.setIsDataLoading);
    const setPreferences = usePreferenceStore((state) => state.setPreferences);
    const checkAllForFilter = useFilterStore((state) => state.checkAllForFilter);
    const applyFiltering = useFilterStore((state) => state.applyFiltering);
    const previewFiltering = useFilterStore((state) => state.previewFiltering);
    const toggleBucket = useFilterStore((state) => state.toggleBucket);
    const checkAppearanceCriteria = useFilterStore((state) => state.checkAppearanceCriteria);
    const resetFilterForGroup = useGroupFilterManagerStore((state) => state.resetFilterForGroup);
    const applyFilteringForGroup = useGroupFilterManagerStore(
        (state) => state.applyFilteringForGroup
    );
    const checkGroupAppearanceCriteria = useGroupFilterManagerStore(
        (state) => state.checkAppearanceCriteria
    );
    const fillGroupAllBuckets = useGroupFilterManagerStore((state) => state.fillAllBuckets);
    const fillAllBuckets = useFilterStore((state) => state.fillAllBuckets);
    const getFilterById = useFilterStore((state) => state.getFilterById);
    const setForceShowPreview = useFilterStore((state) => state.setForceShowPreview);
    const checkedBucketIds = useFilterStore((state) => state.checkedBucketIds);
    const confidence = useConfigurationStore((state) => state.confidence);
    const setConfidence = useConfigurationStore((state) => state.setConfidence);
    const status = useConfigurationStore((state) => state.status);
    const setStatus = useConfigurationStore((state) => state.setStatus);
    const inMonitoredState = useMonitorStore((state) => state.inMonitoredState);

    // <------------------ End of States from ZVS Stores ------------------>

    const getKeycloakToken: () => Observable<string> = izoaMap.get("keycloakToken");

    if (process.env.IS_LOCAL_DEV === "true") {
        // Use dataProvider instead of mockDataProvider to use graph data from api while in dev mode
        zvsDataProviderRegistry["zdsProvider"] = mockDataProvider;
    } else {
        zvsDataProviderRegistry["zdsProvider"] = dataProvider;
    }

    // <------------------ Helper Functions ------------------>

    const severityCheckboxFilter = getFilterById("filter-by-severity") as CheckboxFilter;
    const typeCheckboxFilter = getFilterById("filter-by-type") as CheckboxFilter;
    const subsystemCheckboxFilter = getFilterById("filter-by-subsystem") as CheckboxFilter;

    /**
     * This function is passed to ZVS to determine if a node should be selectable. For Ensemble a node is selectable if it is a monitored node.
     * @param {ELKNode} node - The `node` parameter is of type `ELKNode`.
     * @returns the value of `!node.devData.isUnmonitored`.
     */
    const multiselectAppearanceCriteria = (node: ELKNode) => {
        return !node.devData.isUnmonitored;
    };

    const handleDSGStatusDecorators = (graphNodes: ELKNode[]) => {
        for (const groupNode of graphNodes) {
            if (isGroupNode(groupNode) && groupNode.metadata?.nodes?.length > 0) {
                // If a group node has a monitored node then set the group node as monitored
                const hasMonitoredChildNode = groupNode.metadata.nodes.some(
                    (childNode: ELKNode) => !childNode.devData.isUnmonitored
                );
                groupNode.devData.isUnmonitored = !hasMonitoredChildNode;

                // Sorts nodes in group node by severity and node at index 0 will have highest severity
                // Group node's decorator will be set to that severity
                if (izoaDashboardData.showDataSharingGroups && isSharingGroupNode(groupNode)) {
                    groupNode.metadata.nodes.sort(compareSeverity);
                    const activeSeverityFilterBuckets = severityCheckboxFilter.activeBuckets;
                    const activeSubsystemFilterBuckets = subsystemCheckboxFilter.activeBuckets;
                    const filteredNodes = groupNode.metadata.nodes.filter(
                        (node: ELKNode) =>
                            activeSeverityFilterBuckets.has(
                                Array.from(nodeToBucketIdsForSeverity(node))[0]
                            ) &&
                            activeSubsystemFilterBuckets.has(
                                Array.from(nodeToBucketIdsBySystem(node))[0]
                            )
                    );
                    if (filteredNodes && filteredNodes.length > 0) {
                        filteredNodes.sort(compareSeverity);
                        const firstNode = filteredNodes[0];

                        if (!firstNode.metadata.decorator) {
                            groupNode.metadata.decorator = null;
                        } else {
                            groupNode.metadata.decorator = getStatusDecoratorInfo(
                                firstNode.devData.resource.eventGroupSeverity
                            );
                            // Add custom status decorator replacement to be displayed at the top of the properties panel
                            // (For group nodes, based off of the most severe node.)
                            groupNode.devData.replacementDecoratorElement = (
                                <ReplacementDecoratorForPanel node={firstNode} />
                            );
                            groupNode.devData.showReplacementDecoratorInProperties = true;
                        }
                    }
                    // Recreate group node metadata order because sort reordered them
                    // Group node uses this order when moving nodes out and into a group node
                    groupNode.metadata.ids = [];
                    groupNode.metadata.names = [];
                    groupNode.metadata.types = [];
                    groupNode.metadata.typeIds = [];
                    groupNode.metadata?.nodes.forEach((node: ELKNode) => {
                        const nodeLabel = node.nodeLabel;
                        const nodeTypeLabel = node.nodeLabel;

                        groupNode.metadata.ids.push(node.id);
                        groupNode.metadata.names.push(nodeLabel);
                        groupNode.metadata.types.push(nodeTypeLabel);
                        groupNode.metadata.typeIds.push(node.typeId);
                    });
                }
                // Reset group node filters and update buckets
                resetFilterForGroup(groupNode.id);
                applyFilteringForGroup(groupNode.id, groupNode.metadata.nodes);
                checkGroupAppearanceCriteria(groupNode, groupNode.metadata.nodes);
                fillGroupAllBuckets(groupNode.id, groupNode.metadata.nodes, true);
            }
        }
    };

    /**
     * The function `processFilterMessage` handles different actions and updates the filter settings
     * based on the message received.
     * @param {PostFilterMessage} msg - The `msg` parameter is an object of type `PostFilterMessage`.
     * It contains the following properties: type, action, and data.
     */
    const processFilterMessage = async (msg: PostFilterMessage, skipGetResources?: boolean) => {
        if (msg.action === FilterAction.Cancel) {
            setForceShowPreview(false);
        }

        if (msg.data?.eventGroupSeverity && msg.data.eventGroupSeverity.length > 0) {
            severityCheckboxFilter.uncheckAll();
            for (const ensembleSeverityFilterItem of msg.data.eventGroupSeverity) {
                const eventGroupSeverity = getNumberFromEnsembleFilterValue(
                    ensembleSeverityFilterItem.value
                );

                if (eventGroupSeverity) {
                    const bucketId =
                        Object.keys(EventGroupSeverity)[
                            Object.values(EventGroupSeverity).indexOf(eventGroupSeverity)
                        ];
                    if (bucketId) {
                        toggleBucket("filter-by-severity", bucketId);
                    }
                }
            }
        } else {
            severityCheckboxFilter.checkAll();
            handleDSGStatusDecorators(graphNodes);
        }
        if (!severityCheckboxFilter.isChecked("AlwaysOn")) {
            toggleBucket("filter-by-severity", "AlwaysOn");
        }

        if (msg.data?.resourceType && msg.data.resourceType.length > 0) {
            typeCheckboxFilter.uncheckAll();

            for (const ensembleTypeFilterItem of msg.data.resourceType) {
                if (typeof ensembleTypeFilterItem.value === "string") {
                    const selectedType = getZvsTypeFromEnsembleTypeName(
                        ensembleTypeFilterItem.value
                    );
                    // Address DSG for DB2 and IMS filtering
                    // TODO: Address DSG filtering for the rest of the subsystems
                    if (ensembleTypeFilterItem.value === getProperType.db2subsystem.ensemble) {
                        toggleBucket("filter-by-type", selectedType);
                        toggleBucket("filter-by-type", getProperType.db2datasharinggroup.zvs);
                    } else if (
                        ensembleTypeFilterItem.value === getProperType.imssubsystem.ensemble
                    ) {
                        toggleBucket("filter-by-type", selectedType);
                        toggleBucket("filter-by-type", getProperType.imssysplexgroup.zvs);
                    } else {
                        toggleBucket("filter-by-type", selectedType);
                    }
                }
            }

            if (!typeCheckboxFilter.isChecked("sysplex")) {
                toggleBucket("filter-by-type", getProperType.sysplex.zvs);
            }
        } else {
            typeCheckboxFilter.checkAll();
        }

        if (msg.data?.subsystem && msg.data.subsystem.length > 0) {
            subsystemCheckboxFilter.uncheckAll();
            // We assume that the data only contains subsystems which are selected.
            const selectedSubsystems: string[] = msg.data.subsystem.map(
                (option: EnsembleFilterItem) => option.content
            );
            for (const subsystem of selectedSubsystems) {
                toggleBucket("filter-by-subsystem", subsystem);
            }
        } else {
            subsystemCheckboxFilter.checkAll();
        }

        if (msg.action === FilterAction.Preview) {
            previewFiltering(elkNodes, graphEdges);
            setForceShowPreview(true);
        } else if (msg.action === FilterAction.Apply) {
            setStoredFilterMessage(msg);
            if (msg.data?.status && msg.data?.confidence) {
                const confidence = msg.data.confidence;
                const status = msg.data.status;
                setConfidence(confidence);
                setStatus(status);
                if (!skipGetResources) {
                    const resourceGroupSummary = await getResourceGroupSummary(
                        fromDate.toISOString(),
                        toDate.toISOString(),
                        confidence.from,
                        confidence.to,
                        (status.value as StatusConfig) ?? StatusConfig.All
                    );
                    izoaDashboardData.resourceGroupSummary = resourceGroupSummary;
                }
                // Goes through every node and adds/removes the decorator based on status and confidence
                processResourceGroupSummaryForNodes(elkNodes);
            }
            // Process the nodes in groups to update the group node decorator
            setShouldProcessGroupNodes(true);

            applyFiltering(elkNodes, graphEdges);
            setForceShowPreview(false);
        }
    };

    /**
     * The function `channelHandler` handles different types of broadcast messages and performs
     * corresponding actions based on the message type.
     * @param {BroadcastMessage} message - The `message` parameter is of type `BroadcastMessage`.
     */
    const channelHandler = useCallback(
        (message: BroadcastMessage) => {
            //TODO Handle the sysplex and system filter and to handle resetting confidence/status to the default values and run the code that handles when confidence/status changes.
            switch (message.type) {
                case MessageType.RESET_FILTER:
                    processFilterMessage(resetMessage);
                    break;
                case MessageType.FILTER_DATA:
                    const msg = message as PostFilterMessage;
                    processFilterMessage(msg);
                    break;
                case MessageType.TIME_SELECTED:
                    const timeSelectedMessage = message as TimeSelectedMessage;
                    const selection = timeSelectedMessage.timePickerSelection;
                    setFromDate(new Date(Date.parse(selection.fromDate)));
                    setToDate(new Date(Date.parse(selection.toDate)));
                    setResetHeuristicKey(new Date().getTime());
                default:
                    break;
            }
        },
        // Update channelHandler function with most up to date values of dependency array
        [elkNodes, graphEdges]
    );

    const populateTopology = async () => {
        setIsDataLoading(true);

        izoaDashboardData.getKeycloakToken = getKeycloakToken;

        const resourceGroupSummary = await getResourceGroupSummary(
            fromDate.toISOString(),
            toDate.toISOString(),
            confidence.from,
            confidence.to,
            (status.value as StatusConfig) ?? StatusConfig.All
        );
        izoaDashboardData.resourceGroupSummary = resourceGroupSummary;
        // Get sysplexes from resource group summary api call and get graph data for them
        const sysplexes = Array.from(
            new Set(resourceGroupSummary.map((resource) => resource.clusterName))
        );

        // get the subsystem to focus on for LIC from IZAA
        if (sysplexes.length > 0) {
            if (sysplexes.length > 1) {
                // Set zvs store state late so that loading state works
                setIZAAState();
                setFeatureValue(FeatureConfigOptions.Additive, true);
                const zdsNodeIds: string[] = [];
                for (const sysplex of sysplexes) {
                    const zdsType = getProperType.sysplex.zds;
                    zdsNodeIds.push(`${sysplex}-${zdsType}`);
                }

                setAllSelectedNodeIds(zdsNodeIds);
            } else if (sysplexes.length === 1) {
                // Set zvs store state late so that loading state works
                setIZAAState();
                setFeatureValue(FeatureConfigOptions.Additive, false);

                const sysplex = sysplexes[0];
                const zdsType = getProperType.sysplex.zds;
                const zvsType = getProperType.sysplex.zvs;
                const zdsNodeId = `${sysplex}-${zdsType}`;

                // Setting to specific ZDS node on the topology view
                setSelectedNodeIdAndTypeId(zdsNodeId, zvsType, getType);

                // Set the label of the node, used for the header of the table view
                setSelectedNodeLabel(sysplex);
            }
        }
        setShouldLoadInitialFilters(true);
    };

    const setIZAAState = () => {
        setRedrawOnFilter(true); //TODO: set it back to true once redraw function is fixed.
        setPreferences({ "depth-pref": "3" });
        // Get all the ZDS types from our constants
        const zdsTypes = Object.keys(getProperType);
        const blankProject = {
            id: "",
            uid: "zdsProvider",
            type: "zDLA",
            dataProviderId: "zdsProvider",
            dataProviderName: "",
            lastScanned: "2021-06-01:10:00:00",
            typeIds: zdsTypes,
            metadata: ""
        };

        const featureConfig: FeatureConfig = {
            ...defaultFeatureConfig,
            search: false,
            view: false,
            depth: false,
            history: false,
            filter: false,
            export: false,
            multiselect: true,
            miniMapExpanded: false,
            miniMapInitialZoomSize: 0.7,
            showPropertiesLinks: true,
            showRedrawOption: true,
            handleFillFilterBucketsOnConsumer: true
        };
        setFeatureConfig(featureConfig);
        setDefaultState(blankProject);
        setConfig({
            multiselectAppearanceCriteria: multiselectAppearanceCriteria
        });
        setView(View.Graph);
    };

    const presetSeverityBuckets = () => {
        const severityCheckboxFilter = getFilterById("filter-by-severity") as CheckboxFilter;
        const severityBuckets: {
            [bucketId: string]: Set<string>;
        } = {};
        for (const key of Object.keys(severityCheckboxFilter.bucketLabels)) {
            severityBuckets[key] = new Set();
        }
        severityCheckboxFilter.buckets = severityBuckets;
    };

    const initializeFilters = (nodes: ELKNode[], edges: ELKEdge[]) => {
        if (shouldLoadInitialFilters && nodes.length > 0) {
            presetSeverityBuckets();

            // Load initial filters
            const initialFilterData = izoaMap.get("eventData")?.["filter"];
            const filterMsg: PostFilterMessage = {
                action: FilterAction.Apply,
                data: initialFilterData,
                id: DefaultBroadcastMessageId,
                type: MessageType.FILTER_DATA
            };

            checkAppearanceCriteria(nodes, edges);
            fillAllBuckets(nodes, edges);
            if (inMonitoredState) {
                checkAllForFilter("filter-by-monitor");
                toggleBucket("filter-by-monitor", "Unmonitored");
            }

            processFilterMessage(filterMsg, true);
            setShouldLoadInitialFilters(false);
        }
    };
    // <------------------ End of Helper Functions ------------------>

    useEffect(() => {
        initializeFilters(elkNodes, graphEdges);
    }, [elkNodes, graphEdges, shouldLoadInitialFilters]);

    useEffect(() => {
        topologyChannel.addEventListener("message", channelHandler);

        return () => {
            topologyChannel.removeEventListener("message", channelHandler);
        };
    }, [channelHandler]);

    useEffect(() => {
        // Replicating _FillFilterBuckets.tsx in samwise without doing checkAll
        checkAppearanceCriteria(elkNodes, graphEdges);
        fillAllBuckets(elkNodes, graphEdges);
    }, [elkNodes, graphEdges]);

    // This useEffect determines if the selected resources bar is visible or not. Hidden if 0 checked nodes, visible if > 0 checked nodes
    useEffect(() => {
        setShowSelectedResourcesBar(checkedNodes.length > 0);
    }, [checkedNodes]);

    useEffect(() => {
        //tell framework the insightpack is loaded
        const msg: StopLoadingMessage = {
            type: MessageType.STOP_LOADING,
            id: DefaultBroadcastMessageId,
            appId: izoaMap.get("id")
        };
        topologyChannel.postMessage(msg);

        (async () => {
            // using the cicsregion type to test if scorecard action has already been added
            const cicsType = getType("cicsregion");
            const cicsActions = cicsType["getActions"];
            /*
             * Add view scorecard to cicsregion and db2subsystem type
             */
            const newGetActions = (
                node: ELKNode,
                stateData: ZvsStateData,
                getType: (_typeId: string) => ArtifactRenderType
            ) => {
                /*
                 * Only add viewResourceDetails if node is monitored.
                 * Otherwise use the original actions on a node
                 */
                if (node.devData?.isUnmonitored) {
                    const originalActions = cicsActions(node, stateData, getType);
                    return originalActions;
                }

                const viewResourceDetailsAction: ActionType = {
                    id: "viewResourceAction",
                    label: "View resource details",
                    renderIcon: () => <HeatMap_03 />,
                    action: () => {
                        const msg: ViewResourceDetailsMessage = {
                            id: DefaultBroadcastMessageId,
                            type: MessageType.VIEW_RESOURCE_DETAILS,
                            selectedResources: [node?.devData?.resource]
                        };
                        topologyChannel.postMessage(msg);
                    }
                };

                return [viewResourceDetailsAction];
            };
            appendActionsToType("cicsregion", newGetActions);
            appendActionsToType("db2subsystem", newGetActions);
            appendActionsToType("mqsubsystem", newGetActions);
            appendActionsToType("imssubsystem", newGetActions);
            appendActionsToType("zos", newGetActions);

            populateTopology();
        })();

        /*
         * Sets the ZVS state back to default and cleans up useEffect when component is unmounted
         */
        return () => {
            setDefaultState();
        };
    }, [resetHeuristicKey]);

    useEffect(() => {
        if (shouldProcessGroupNodes && graphNodes.length > 0) {
            handleDSGStatusDecorators(graphNodes);
            setShouldProcessGroupNodes(false);
            // To make data sharing group filterable by severity filter
            // Not sure if checkAppearanceCriteria is needed
            checkAppearanceCriteria(elkNodes, graphEdges);
            fillAllBuckets(elkNodes, graphEdges);
        }
    }, [graphNodes, shouldProcessGroupNodes, elkNodes]);

    //This useEffect ensures all filters are preserved once you toggle on or off the group by DSG toggle.
    useEffect(() => {
        // Reapply filter after group by data sharing group is toggled
        if (storedFilterMessage) {
            // If resource type or severity data was empty then send empty again since there were no filters to turn off
            if (
                storedFilterMessage.data?.resourceType &&
                storedFilterMessage.data.resourceType.length > 0
            ) {
                //persist filtered type
                const persistentActiveTypeFilter = Array.from(checkedBucketIds)
                    .filter((type: string) => getProperType[type])
                    .flatMap((type) => {
                        const ensembleTypeFilterItem = getProperType[type].ensembleFilterType;
                        return ensembleTypeFilterItem ? [ensembleTypeFilterItem] : [];
                    });

                storedFilterMessage.data.resourceType = persistentActiveTypeFilter;
            }
            if (
                storedFilterMessage.data?.eventGroupSeverity &&
                storedFilterMessage.data.eventGroupSeverity.length > 0
            ) {
                //persist filtered severity
                const persistentActiveSeverityFilter = Array.from(checkedBucketIds)
                    .filter((severity: string) => validSeveritiesArray.includes(severity))
                    .flatMap((severity) => {
                        // FlatMap acts like a map and filters out nulls in the array
                        const ensembleSeverityFilterItem =
                            EnsembleSeverityFilterValues[
                                EventGroupSeverity[severity as keyof typeof EventGroupSeverity]
                            ];
                        return ensembleSeverityFilterItem ? [ensembleSeverityFilterItem] : [];
                    });

                storedFilterMessage.data.eventGroupSeverity = persistentActiveSeverityFilter;
            }
            if (
                storedFilterMessage.data?.subsystem &&
                storedFilterMessage.data.subsystem.length > 0
            ) {
                const subsystemFilter = getFilterById("filter-by-subsystem") as CheckboxFilter;
                const validBuckets = Object.keys(subsystemFilter.buckets);
                //persist filtered subsystems
                const persistentActiveSubsystemFilters: EnsembleFilterItem[] = Array.from(
                    checkedBucketIds
                )
                    .filter((subsystem) => validBuckets.includes(subsystem))
                    .map((subsystem) => ({
                        content: subsystem,
                        selected: true
                    }));
                storedFilterMessage.data.subsystem = persistentActiveSubsystemFilters;
            }

            processFilterMessage(storedFilterMessage);
        }

        if (inMonitoredState) {
            checkAllForFilter("filter-by-monitor");
            toggleBucket("filter-by-monitor", "Unmonitored");
            applyFiltering(elkNodes, graphEdges);
        } else {
            checkAllForFilter("filter-by-monitor");
            applyFiltering(elkNodes, graphEdges);
        }
        setShouldProcessGroupNodes(true);
    }, [izoaDashboardData.showDataSharingGroups]);

    return (
        <TopologyContainer>
            <SysplexSystemFilter />
            <ConditionalTag condition={process.env.IS_LOCAL_DEV === "true"}>
                <MockEnsembleChannel />
            </ConditionalTag>
            <SeverityFilters />
            <MonitorFilters />
            <GroupNodeEnsembleFilters />
            <StyledToolbarContainer>
                <ConditionalTag condition={!showSelectedResourcesBar}>
                    <EnsembleToolbar />
                </ConditionalTag>
                <ConditionalTag condition={showSelectedResourcesBar}>
                    <SelectedResourcesBar />
                </ConditionalTag>
            </StyledToolbarContainer>
            <ZVisualization />
        </TopologyContainer>
    );
};

class ZdiscoveryElement extends HTMLElement {
    izoaData!: UrlParams;

    connectedCallback() {
        if (process.env.IS_LOCAL_DEV === "true") {
            // DEV Only: For development only when using mock data
            const mockIzoaData: UrlParams = generateMockData();
            ReactDOM.render(<App izoaData={mockIzoaData} />, this);
        } else {
            ReactDOM.render(<App izoaData={this.izoaData} />, this);
        }
    }

    // This gets called when a user closes the topology tab on the pi dashboard
    // And this unmounts the react component
    disconnectedCallback() {
        ReactDOM.unmountComponentAtNode(this);
    }
}

if (!customElements.get(CustomElementId)) {
    customElements.define(CustomElementId, ZdiscoveryElement);
}

export default App;
