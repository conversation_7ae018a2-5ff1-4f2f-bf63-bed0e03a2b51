- name: Build Discovery UI docker image
  # hosts: myhost
  hosts: "{{ host }}"
  remote_user: ansible
  vars:
      - destdir: /home/<USER>/git
  tasks:
      - name: debug
        debug:
            msg: Artifactory user "{{artifactoryUser}}" - Branch "{{branch}}"

      - name: "Clone a github repository"
        git:
            repo: ******************:IZOA/zaa-ensemble-zdiscovery.git
            dest: /home/<USER>/zdiscovery_ui_github
            version: "{{branch}}"
            force: true
            clone: yes
            update: yes
        delegate_to: localhost

      - name: "Copying code from Ansible to the host as root"
        become: true
        copy:
            src: /home/<USER>/zdiscovery_ui_github/
            dest: "{{ destdir }}/zdiscovery-ui"
            owner: ansible
            group: ansible
            mode: "0644"

      # - name: Read-write git checkout from github
      #   git:
      #     repo: ******************:IZOA/zaa-zdiscovery-ui.git
      #     dest: "{{ destdir }}/zdiscovery-ui"
      #     version: "{{branch}}"
      #     force: true

      # - name: "Install NodeJS & NPM"
      #   become: true
      #   package:
      #     name: nodejs
      #     state: present

      - name: Set access to Artifactory (.npmrc) and build Ensemble UI Output.
        shell: |
            cd {{ destdir }}/zdiscovery-ui/
            touch .npmrc
            npm config set @zvisualization:registry https://eu.artifactory.swg-devops.com/artifactory/api/npm/sys-nazare-cicd-team-ui-npm-virtual
            echo "registry=https://na.artifactory.swg-devops.com/artifactory/api/npm/sys-izoa-npm-virtual/" >> .npmrc
            curl -u {{artifactoryUser}}:{{artifactoryToken}} https://eu.artifactory.swg-devops.com/artifactory/api/npm/sys-nazare-cicd-team-ui-npm-virtual/auth/zvisualization >> .npmrc 
            curl -u {{artifactoryUser}}:{{artifactoryToken}} https://na.artifactory.swg-devops.com/artifactory/api/npm/auth/ >> .npmrc

            npm config fix
            rm -r node_modules
            npm install
            npm run build
            # npm run test
            # rm .npmrc
        register: result
        failed_when: ( result.rc not in [ 0 ] )

      - name: Copy Dockerfile to root context
        copy:
            src: "{{ destdir }}/zdiscovery-ui/docker/Dockerfile-dev"
            dest: "{{ destdir }}/zdiscovery-ui/Dockerfile"
            remote_src: true

      - name: Add Ansible to Docker group
        become_user: root
        become: true
        ansible.builtin.user:
            name: ansible
            append: true
            groups: docker

      - name: Log into private registry and force re-authorization
        docker_login:
            registry: docker-na.artifactory.swg-devops.com
            username: "{{artifactoryUser}}"
            password: "{{artifactoryToken}}"
            reauthorize: yes

      - name: build Topology UI image
        docker_image:
            name: icr.io/zoa-oci/zaa-topology-ui:latest-x86_64
            # name: icr.io/zoa-oci/zaa-topology-ui:dev-s390x
            source: build
            build:
                dockerfile: Dockerfile
                nocache: true
                path: "{{ destdir }}/zdiscovery-ui/"
                # platform: "linux/s390x"
                platform: "linux/x86_64"
                # platform: "linux/arm64/v8"
            state: present
            force_source: true

      - name: push to Artifactory
        # shell: docker push icr.io/zoa-oci/zaa-topology-ui:dev-s390x
        shell: docker push icr.io/zoa-oci/zaa-topology-ui:latest-x86_64

      - name: run Topology UI
        docker_container:
            name: zaa-zrddsui
            state: started
            networks:
                - name: ibmzaiops_zaiops
            # image: "icr.io/zoa-oci/zaa-topology-ui:dev-s390x"
            image: "icr.io/zoa-oci/zaa-topology-ui:latest-x86_64"
            hostname: zrddsui
            pull: false
            ports:
                - "4000:4000"
