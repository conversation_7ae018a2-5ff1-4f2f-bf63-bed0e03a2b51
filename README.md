# zaa-ensemble-zdiscovery

Z-Discovery using Module Federation.

Micro frontend React application to handle UI content for Topology.

### Prerequisites

1. Node 16.10.0 and NPM 7.24.0
2. <PERSON>ttier (we use <PERSON><PERSON><PERSON> to format our code)
3. <PERSON><PERSON><PERSON> (we use <PERSON><PERSON><PERSON> to lint our code)

For running this project locally by itself, follow the instructions [here](https://ibm.ent.box.com/notes/894096879137) underneath the section **Standalone deployment**

For running this project within the IZAA PI dashboard UI, follow the instructions [here](https://ibm.ent.box.com/notes/894096879137) underneath the section **Integration deployment**

Currently, when developing locally and testing changes, the UI is making requests to the topology data on another machine. Right now the local UI is connecting the machine called zoastack1.fyre.ibm.com. You can see that machine specified in this [file](https://github.ibm.com/IZOA/zaa-zdiscovery-insights-MF/blob/develop/webpack.dev.config.ts) in the proxy property. To make it easier and quicker in development, that machine only requires a static token to retrieve data.

Therefore, when developing this project locally, remember to set ```const useStaticToken = true;``` instead of ```false``` in [src/App.tsx](https://github.ibm.com/IZOA/zaa-zdiscovery-insights-MF/blob/develop/src/App.tsx). This ensures the code uses just the static token to retrieve data. However, in production, remember to set this same value back to `false`. This ensures that when the code builds, it will use a valid security token from IZAA's keycloak server.

### Upcoming

1. We will plan to add environment variables so switching the values won't have to be done every time
2. Add a "engines" field in the `package.json` to enforce the node and npm version required to use this project
3. Move over some instructions from the box note linked above into this README
