# Build Z Topology UI backend from Nginx common image
FROM icr.io/zoa-oci/zoacommon-nginx-micro:1.27.4-x86_64

ARG REPO=unknown
ARG COMMIT=unknown

LABEL feature="IBM Z Anomaly Analytics common services"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

COPY ./docker/reverse-proxy.conf /etc/nginx/conf.d/default.conf
COPY ./docker/bin/docker-entrypoint.sh ./docker/bin/healthcheck.sh /usr/local/bin/
COPY ./docker/ensemble-topology-ui-MF.tar.gz /

# Provide a non-root user to run the process. \
RUN mkdir -p /usr/local/nginx/html/zrdds-ui \
    && cd /usr/local/nginx/html/zrdds-ui \
    && tar xvzf /ensemble-topology-ui-MF.tar.gz \
    && rm -f /ensemble-topology-ui-MF.tar.gz \
    && cp /usr/local/nginx/conf/mime.types /etc/nginx/conf.d/ \
    && groupadd --gid 1000 nginx \
    && useradd --uid 1000 --gid 1000 -G 0 --home-dir /usr/local/nginx --no-create-home nginx \
    && chmod 755 /usr/local/bin/*.sh \
    && ln -sf /dev/stdout /usr/local/nginx/logs/access.log

USER 1000

STOPSIGNAL SIGQUIT

HEALTHCHECK NONE
# HEALTHCHECK CMD /usr/local/bin/healthcheck.sh

# start app
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
