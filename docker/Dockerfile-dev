# Build Z Topology UI backend from Nginx common image
FROM icr.io/zoa-oci/zoacommon-nginx-micro:1.27.4-x86_64
# FROM icr.io/zoa-oci/zoacommon-nginx:1.25.4-s390x

ARG REPO=unknown
ARG COMMIT=unknown

LABEL feature="IBM Z Anomaly Analytics common services"
LABEL repo=${REPO}
LABEL commit=${COMMIT}

COPY ./docker/reverse-proxy.conf /etc/nginx/conf.d/default.conf
COPY ./docker/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# COPY ./docker/zdiscovery-ui-MF.tar.gz /
COPY ./build/ /usr/local/nginx/html/zrdds-ui

# Provide a non-root user to run the process. \
# RUN mkdir -p /usr/local/nginx/html/zrdds-ui \
#     && cd /usr/local/nginx/html/zrdds-ui \
#     && tar xvzf /zdiscovery-ui-MF.tar.gz \
#     && rm -f /zdiscovery-ui-MF.tar.gz \
RUN cp /usr/local/nginx/conf/mime.types /etc/nginx/conf.d/ \
    && groupadd --gid 1000 nginx \
    && useradd --uid 1000 --gid 1000 -G 0 --home-dir /usr/local/nginx --no-create-home nginx \
    && ln -sf /dev/stdout /usr/local/nginx/logs/access.log

USER 1000

STOPSIGNAL SIGQUIT

# start app
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
