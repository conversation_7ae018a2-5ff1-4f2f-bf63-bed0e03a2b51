error_log stderr info;
worker_processes auto;

# upstream zrdds {
#     server host.docker.internal:8888/zrdds;
# }
events {
}

http {
    log_format      noescape escape=none '$remote_addr - $remote_user [$time_local] $request \n$request_body';
    access_log      /usr/local/nginx/logs/access.log noescape;
    include         mime.types;
    default_type    application/octet-stream;
    server {
        listen       4000;
        server_name  localhost;

        location / {
            root   /usr/local/nginx/html;
            index  index.html index.htm Index.html;
            # try_files $uri $uri/ /index.html =404;
        }


        # location /zrdds {
        #     proxy_pass http://zrdds;
        # }


        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/local/nginx/html;
        }
    }
}
